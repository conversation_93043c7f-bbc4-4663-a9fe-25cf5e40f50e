-- Quick fix for swap request rejection/cancellation issues
-- Run this in Supabase SQL Editor

-- 1. Fix the historico_trocas table to allow NULL membro_destino
ALTER TABLE public.historico_trocas ALTER COLUMN membro_destino DROP NOT NULL;

-- 2. Add missing RLS policies for solicitacoes_troca table
ALTER TABLE public.solicitacoes_troca ENABLE ROW LEVEL SECURITY;

-- Drop any existing conflicting policies
DROP POLICY IF EXISTS "Membros podem ver suas próprias solicitações" ON public.solicitacoes_troca;
DROP POLICY IF EXISTS "Membros podem criar suas próprias solicitações" ON public.solicitacoes_troca;
DROP POLICY IF EXISTS "Membros podem atualizar suas próprias solicitações" ON public.solicitacoes_troca;
DROP POLICY IF EXISTS "Administradores podem ver todas solicitações" ON public.solicitacoes_troca;
DROP POLICY IF EXISTS "Administradores podem atualizar todas solicitações" ON public.solicitacoes_troca;
DROP POLICY IF EXISTS "Membros podem ver solicitações relevantes" ON public.solicitacoes_troca;
DROP POLICY IF EXISTS "Membros podem cancelar suas próprias solicitações" ON public.solicitacoes_troca;
DROP POLICY IF EXISTS "Administradores podem criar solicitações" ON public.solicitacoes_troca;

-- Policy for members to view their own requests and available requests from others
CREATE POLICY "Membros podem ver solicitações relevantes" 
ON public.solicitacoes_troca 
FOR SELECT 
USING (
  -- Own requests
  id_solicitante = current_setting('app.current_member', true) OR
  -- Available requests from others (for candidature)
  (status IN ('aguardando_candidatos', 'aguardando_aprovacao') AND id_solicitante != current_setting('app.current_member', true))
);

-- Policy for members to create their own requests
CREATE POLICY "Membros podem criar suas próprias solicitações" 
ON public.solicitacoes_troca 
FOR INSERT 
WITH CHECK (id_solicitante = current_setting('app.current_member', true));

-- Policy for members to update their own requests (for cancellation)
CREATE POLICY "Membros podem cancelar suas próprias solicitações" 
ON public.solicitacoes_troca 
FOR UPDATE 
USING (
  id_solicitante = current_setting('app.current_member', true) AND
  -- Only allow cancellation of requests in specific statuses
  status IN ('aguardando_candidatos', 'aguardando_aprovacao')
)
WITH CHECK (
  id_solicitante = current_setting('app.current_member', true) AND
  -- Only allow updating to cancelled status or other member-allowed changes
  (status = 'cancelada' OR OLD.status = NEW.status)
);

-- Policy for administrators to view all requests
CREATE POLICY "Administradores podem ver todas solicitações" 
ON public.solicitacoes_troca 
FOR SELECT 
TO authenticated
USING (public.is_admin_user());

-- Policy for administrators to update all requests (for approval/rejection)
CREATE POLICY "Administradores podem atualizar todas solicitações" 
ON public.solicitacoes_troca 
FOR UPDATE 
TO authenticated
USING (public.is_admin_user())
WITH CHECK (public.is_admin_user());

-- Policy for administrators to insert requests (if needed)
CREATE POLICY "Administradores podem criar solicitações" 
ON public.solicitacoes_troca 
FOR INSERT 
TO authenticated
WITH CHECK (public.is_admin_user());

-- 3. Update the history trigger function to handle NULL membro_destino properly
CREATE OR REPLACE FUNCTION public.registrar_historico_troca()
RETURNS TRIGGER 
LANGUAGE plpgsql
SECURITY DEFINER -- This allows the trigger to bypass RLS
AS $$
BEGIN
    -- Register history for status changes to aceita, rejeitada, or cancelada
    IF NEW.status IN ('aceita', 'rejeitada', 'cancelada') AND (OLD.status IS NULL OR OLD.status != NEW.status) THEN
        INSERT INTO public.historico_trocas (
            id_solicitacao,
            tipo_troca,
            membro_origem,
            membro_destino,
            escala_origem_data,
            escala_origem_turno,
            observacoes
        )
        SELECT 
            NEW.id,
            CASE 
                WHEN NEW.status = 'aceita' THEN 'aceita'
                WHEN NEW.status = 'rejeitada' THEN 'rejeitada'
                ELSE 'cancelada'
            END,
            NEW.id_solicitante,
            -- For rejected/cancelled requests, membro_destino can be NULL
            CASE 
                WHEN NEW.status = 'aceita' THEN COALESCE(NEW.candidato_selecionado, NEW.membro_destino)
                ELSE NULL  -- For rejected/cancelled requests, no destination member
            END,
            COALESCE(ep."Data", 'Data não encontrada'),
            COALESCE(ep."Turno", 'Turno não encontrado'),
            CASE 
                WHEN NEW.status = 'aceita' THEN 
                    CONCAT('Troca aprovada pelo administrador em ', NOW()::date, 
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
                WHEN NEW.status = 'rejeitada' THEN 
                    CONCAT('Troca rejeitada pelo administrador em ', NOW()::date,
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
                ELSE 
                    CONCAT('Troca cancelada em ', NOW()::date,
                           CASE WHEN NEW.data_cancelamento IS NOT NULL 
                                THEN CONCAT(' em ', NEW.data_cancelamento::date) 
                                ELSE '' END)
            END
        FROM public."escalasPassadas" ep
        WHERE ep."ID" = NEW.id_escala_origem;
        
        -- Log for debugging
        RAISE NOTICE 'Histórico registrado para solicitação % com status %', NEW.id, NEW.status;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Ensure the trigger exists
DROP TRIGGER IF EXISTS trigger_historico_troca ON public.solicitacoes_troca;
CREATE TRIGGER trigger_historico_troca
    AFTER UPDATE ON public.solicitacoes_troca
    FOR EACH ROW
    EXECUTE FUNCTION public.registrar_historico_troca();
