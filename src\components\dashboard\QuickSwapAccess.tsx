
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, ArrowRight, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface QuickSwapAccessProps {
  pendingCount: number;
}

export const QuickSwapAccess = ({ pendingCount }: QuickSwapAccessProps) => {
  const navigate = useNavigate();

  const handleNavigateToSwaps = () => {
    navigate('/admin-swap-requests');
  };

  return (
    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={handleNavigateToSwaps}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5 text-blue-600" />
          Gerenciamento de Trocas
        </CardTitle>
        <CardDescription>Acesso administrativo para aprovar solicitações</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <div className="text-3xl font-bold text-blue-600">{pendingCount}</div>
            <p className="text-sm text-gray-600">Solicitações no sistema</p>
            {pendingCount > 0 && (
              <p className="text-xs text-orange-600 font-medium mt-1">
                Requer atenção administrativa
              </p>
            )}
          </div>
          <Button variant="outline" size="sm">
            <span>Gerenciar</span>
            <ArrowRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
