
import { supabase } from "@/integrations/supabase/client";
import { Person } from "@/types";
import { SwapRequest } from "@/types/swapTypes";

export async function fetchPersons(): Promise<Person[]> {
  try {
    const response = await fetch("https://webhook.dpscloud.online/webhook/b64b28fc-c5de-4e08-9d9b-e7771af7318a", {
      method: "GET",
      headers: {
        "Content-Type": "application/json"
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch data: ${response.status}`);
    }
    
    const data = await response.json();
    
    console.log("API Response:", data);
    
    // Ensure we have a valid array
    if (!Array.isArray(data)) {
      console.error("API did not return an array:", data);
      return [];
    }
    
    // Process each person to have valid availability values and ensure all required properties exist
    return data.map(person => {
      // Create a safe person object with all required fields
      const safePerson: Person = {
        _lineNumber: person._lineNumber || 0,
        ID: person.ID || `unknown-${Math.random().toString(36).substring(2, 9)}`,
        Nome: person.Nome || "Sem Nome",
        "Segunda-Feira": typeof person["Segunda-Feira"] === 'string' ? person["Segunda-Feira"] : '',
        "Terça-Feira": typeof person["Terça-Feira"] === 'string' ? person["Terça-Feira"] : '',
        "Quarta-Feira": typeof person["Quarta-Feira"] === 'string' ? person["Quarta-Feira"] : '',
        "Quinta-Feira": typeof person["Quinta-Feira"] === 'string' ? person["Quinta-Feira"] : '',
        "Sexta-Feira": typeof person["Sexta-Feira"] === 'string' ? person["Sexta-Feira"] : '',
        "Sabado": typeof person["Sabado"] === 'string' ? person["Sabado"] : '',
        "Domingo": typeof person["Domingo"] === 'string' ? person["Domingo"] : ''
      };
      
      return safePerson;
    }).filter(person => 
      // Filter out any invalid entries (must have ID and Nome)
      person && person.ID && person.Nome
    );
  } catch (error) {
    console.error("Error fetching persons:", error);
    return [];
  }
}

// Define a type for report data
export interface Report {
  data: string;
  id: string;
  "Nome Lider": string;
  "Tipo Culto": string;
  Faltas: string;
  carros: number;
  motos: number;
  bikes: number;
  "Alteração ?": string;
  Motorista1: string;
  tel_moto: string;
  Motorista2: string;
  tel_moto2: string;
  Testemunha1: string;
  "Tel Testemunha": string;
  Testemunha2: string;
  "Tel Testemunha2": string;
  Vitima1: string;
  "Tel vit 1": string;
  Vitima2: string;
  "Tel vit 2": string;
  Vitima3: string;
  "Tel vit 3": string;
  Vitima4: string;
  "Tel vit 4": string;
  Vitima5: string;
  "Tel vit 5": string;
  Relator: string;
  Relatado: string;
  "Tel relator": string;
  "Versão sentinela": string;
  Desatentos: string;
  "enviado lider ?": string;
  [key: string]: any; // Add index signature for any additional fields
}

// Parse Brazilian date format (DD/MM/YYYY) to Date object for sorting
function parseBrazilianDate(dateStr: string): Date {
  if (!dateStr) return new Date(0);
  const [day, month, year] = dateStr.split('/').map(Number);
  return new Date(year, month - 1, day);
}

export async function fetchReports(): Promise<Report[]> {
  try {
    const endpoint = "https://webhook.dpscloud.online/webhook/dbd537e9-f41d-43d3-8f2c-ea0179b08de1";
      
    console.log("Using reports endpoint:", endpoint);
      
    const response = await fetch(endpoint, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      cache: "no-store"
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error response from server:", errorText);
      throw new Error(`Failed to fetch reports: ${response.status}`);
    }
    
    const responseText = await response.text();
    console.log("Raw response:", responseText.substring(0, 200) + "...");
    
    try {
      const data = JSON.parse(responseText);
      console.log("Reports API Response:", data.length, "items");
      
      if (!Array.isArray(data)) {
        console.error("Reports API did not return an array:", data);
        return [];
      }
      
      // Transform the data to ensure numerical values are properly parsed
      const reports = data.map(item => ({
        ...item,
        carros: typeof item.carros === 'string' ? parseInt(item.carros) || 0 : item.carros || 0,
        motos: typeof item.motos === 'string' ? parseInt(item.motos) || 0 : item.motos || 0,
        bikes: typeof item.bikes === 'string' ? parseInt(item.bikes) || 0 : item.bikes || 0,
        Faltas: item.Faltas || "0"
      }));
      
      // Sort by date - newest first
      return reports.sort((a, b) => {
        const dateA = parseBrazilianDate(a.data);
        const dateB = parseBrazilianDate(b.data);
        return dateB.getTime() - dateA.getTime();
      });
    } catch (e) {
      console.error("Error parsing JSON response:", e);
      throw new Error(`Failed to parse JSON response: ${e}`);
    }
  } catch (error) {
    console.error("Error fetching reports:", error);
    return [];
  }
}

// Helper function to check if a report has an incident/alteration
export function hasIncident(report: Report): boolean {
  return report["Alteração ?"] !== "NÃO. TUDO EM PAZ";
}

// Helper function to count total absences from reports
export function countTotalAbsences(reports: Report[]): number {
  return reports.reduce((total, report) => {
    const absences = parseInt(report.Faltas) || 0;
    return total + absences;
  }, 0);
}

// Helper function to format date to DD/MM/YYYY format
export function formatDateForAPI(date: Date): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}/${month}/${year}`;
}

// Helper function to get non-empty fields from a report
export function getNonEmptyFields(report: Report): Record<string, any> {
  const result: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(report)) {
    // Skip empty strings, null, undefined, or "NÃO. TUDO EM PAZ" for "Alteração ?" field
    if (
      value !== "" && 
      value !== null && 
      value !== undefined && 
      !(key === "Alteração ?" && value === "NÃO. TUDO EM PAZ")
    ) {
      result[key] = value;
    }
  }
  
  return result;
}

export async function fetchPendingSwapsCount(): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('solicitacoes_troca')
      .select('*', { count: 'exact', head: true })
      .or('status.neq.cancelada,status.neq.concluida,status.neq.vencida');

    if (error) {
      console.error('Error fetching pending swaps count:', error);
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('Failed to fetch pending swaps count:', error);
    return 0;
  }
}

export async function fetchMemberSwapRequests(memberId: string): Promise<SwapRequest[]> {
  try {
    const { data, error } = await supabase
      .from('solicitacoes_troca')
      .select('*')
      .or(`id_solicitante.eq.${memberId},membro_destino.eq.${memberId}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching member swap requests:', error);
      throw error;
    }

    if (!data) return [];

    // Validar e converter os dados para o tipo SwapRequest
    return data.map(item => ({
      ...item,
      status: item.status as 'pendente' | 'aguardando_candidatos' | 'aguardando_aprovacao' | 'aceita' | 'rejeitada',
      id_solicitante: item.id_solicitante || '',
      membro_destino: item.membro_destino || '',
      id_escala_origem: item.id_escala_origem || '',
      id_escala_destino: item.id_escala_destino || '',
      data_solicitacao: item.data_solicitacao || '',
      data_resposta: item.data_resposta || '',
      candidato_selecionado: item.candidato_selecionado || '',
      data_selecao_candidato: item.data_selecao_candidato || '',
      created_at: item.created_at || '',
      updated_at: item.updated_at || ''
    }));
  } catch (error) {
    console.error('Failed to fetch member swap requests:', error);
    return [];
  }
}
