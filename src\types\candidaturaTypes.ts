
export interface Candidatura {
  id: string;
  id_solicitacao: string;
  membro_candidato: string;
  data_candidatura: string;
  status: 'ativa' | 'selecionada' | 'rejeitada';
  observacoes?: string;
  created_at: string;
  updated_at: string;
}

export interface SolicitacaoComCandidaturas {
  id: string;
  id_solicitante: string;
  id_escala_origem: string;
  id_escala_destino?: string;
  membro_destino?: string;
  tipo_solicitacao: string;
  mensagem?: string;
  status: 'pendente' | 'aguardando_candidatos' | 'aguardando_aprovacao' | 'aceita' | 'rejeitada';
  data_solicitacao: string;
  data_resposta?: string;
  candidato_selecionado?: string;
  data_selecao_candidato?: string;
  observacoes_admin?: string;
  responsavel_aprovacao?: string;
  created_at: string;
  updated_at: string;
  candidaturas?: Candidatura[];
}
