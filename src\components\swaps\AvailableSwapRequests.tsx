import React, { useState, useMemo, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { SwapRequest, ScheduleItem } from '@/types/swapTypes';
import { useCandidaturas } from '@/hooks/useCandidaturas';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { AvailableSwapRequestsHeader } from './AvailableSwapRequestsHeader';
import { AvailableSwapRequestsEmpty } from './AvailableSwapRequestsEmpty';
import { AvailableSwapRequestsList } from './AvailableSwapRequestsList';
import { CandidateDialog } from './CandidateDialog';
import { debouncedInvalidateMultipleQueries } from '@/utils/cacheUtils';

interface AvailableSwapRequestsProps {
  allPendingSwapRequests: SwapRequest[] | undefined;
  memberSchedules: ScheduleItem[] | undefined;
}

const hasDateConflict = (scheduleDateStr: string | null, memberSchedules: ScheduleItem[] | undefined): boolean => {
  if (!scheduleDateStr || !memberSchedules) return false;

  try {
    const [day, month, year] = scheduleDateStr.split('/');
    if (!day || !month || !year) return false;
    const targetDate = new Date(`${year}-${month}-${day}`);
    targetDate.setUTCHours(0, 0, 0, 0);

    return memberSchedules.some(schedule => {
      if (!schedule.Data) return false;
      const [memberDay, memberMonth, memberYear] = schedule.Data.split('/');
      if (!memberDay || !memberMonth || !memberYear) return false;
      const memberScheduleDate = new Date(`${memberYear}-${memberMonth}-${memberDay}`);
      memberScheduleDate.setUTCHours(0, 0, 0, 0);
      return targetDate.getTime() === memberScheduleDate.getTime();
    });
  } catch (error) {
    console.error("Error parsing date for conflict check:", error);
    return false;
  }
};

export const AvailableSwapRequests = ({ allPendingSwapRequests, memberSchedules }: AvailableSwapRequestsProps) => {
  const { auth } = useMemberAuth();
  const queryClient = useQueryClient();
  const { createCandidaturaMutation, minhasCandidaturas, isCreatingCandidatura } = useCandidaturas();
  const [selectedRequest, setSelectedRequest] = useState<string | null>(null);
  const [observacoes, setObservacoes] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);

  // Atualizar dados quando o componente for renderizado (mudança de aba)
  useEffect(() => {
    debouncedInvalidateMultipleQueries(queryClient, [
      ['all-pending-swap-requests'],
      ['candidaturas'],
      ['minhas-candidaturas']
    ], 100);
  }, []); // Executar apenas uma vez quando o componente for montado

  const memberName = auth.member?.['Nome Escala'];

  // Buscar dados das escalas para as solicitações
  const { data: scheduleDataMap = {} } = useQuery({
    queryKey: ['schedule-data-for-swaps', allPendingSwapRequests?.map(r => r.id_escala_origem)],
    queryFn: async () => {
      if (!allPendingSwapRequests?.length) return {};

      const scheduleIds = allPendingSwapRequests
        .map(req => req.id_escala_origem)
        .filter(Boolean);

      if (scheduleIds.length === 0) return {};

      const { data } = await supabase
        .from('escalasPassadas')
        .select('ID, Data, Turno, Dia, Lider, Membro1, Membro2, Membro3, Membro4') // Selecionar apenas campos necessários
        .in('ID', scheduleIds);

      if (!data) return {};

      const scheduleMap: Record<string, any> = {};
      data.forEach(schedule => {
        scheduleMap[schedule.ID] = schedule;
      });
      return scheduleMap;
    },
    enabled: !!allPendingSwapRequests?.length,
    staleTime: 10 * 60 * 1000, // 10 minutos
    gcTime: 30 * 60 * 1000, // 30 minutos
  });

  // Filtrar solicitações e adicionar info de conflito
  const availableRequestsWithInfo = useMemo(() => {
    if (!allPendingSwapRequests || !memberName) return [];

    return allPendingSwapRequests
      .filter(request => {
        // Excluir solicitações do próprio membro
        if (request.id_solicitante === memberName) return false;
        
        // Incluir apenas solicitações aguardando candidatos ou aprovação
        return request.status === 'aguardando_candidatos' || request.status === 'aguardando_aprovacao';
      })
      .map(request => {
        const scheduleData = scheduleDataMap[request.id_escala_origem];
        const conflict = scheduleData ? hasDateConflict(scheduleData.Data, memberSchedules) : false;
        
        return {
          ...request,
          hasDateConflict: conflict,
        };
      });
  }, [allPendingSwapRequests, memberName, scheduleDataMap, memberSchedules]);

  const handleOpenCandidateDialog = (requestId: string) => {
    setSelectedRequest(requestId);
    setObservacoes('');
    setDialogOpen(true);
  };

  const handleCandidatar = () => {
    if (!selectedRequest) return;

    createCandidaturaMutation.mutate(
      { 
        idSolicitacao: selectedRequest, 
        observacoes: observacoes.trim() || undefined 
      },
      {
        onSuccess: () => {
          setDialogOpen(false);
          setObservacoes('');
          setSelectedRequest(null);
        }
      }
    );
  };

  // Verificar se o membro já se candidatou para uma solicitação específica
  const hasCandidateFor = (requestId: string) => {
    return minhasCandidaturas?.some(
      c => c.id_solicitacao === requestId && c.status === 'ativa'
    ) || false;
  };

  if (availableRequestsWithInfo.length === 0) {
    return <AvailableSwapRequestsEmpty />;
  }

  const hasRequestsAwaitingApproval = availableRequestsWithInfo.some(req => req.status === 'aguardando_aprovacao');
  const trulyAvailableCount = availableRequestsWithInfo.filter(r => !r.hasDateConflict && !hasCandidateFor(r.id)).length;

  return (
    <>
      <Card>
        <AvailableSwapRequestsHeader
          availableRequestsCount={trulyAvailableCount}
          hasRequestsAwaitingApproval={hasRequestsAwaitingApproval}
        />
        <AvailableSwapRequestsList
          availableRequests={availableRequestsWithInfo}
          scheduleDataMap={scheduleDataMap}
          onCandidate={handleOpenCandidateDialog}
          isCreatingCandidatura={isCreatingCandidatura}
          selectedRequest={selectedRequest}
          hasCandidateFor={hasCandidateFor}
        />
      </Card>

      <CandidateDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        observacoes={observacoes}
        onObservacoesChange={setObservacoes}
        onConfirm={handleCandidatar}
        isCreating={isCreatingCandidatura}
      />
    </>
  );
};
