
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { 
  fetchSwapsWithCandidates, 
  fetchSwapsAwaitingCandidates,
  fetchAllSwapRequestsCount,
  fetchApprovedSwapsCount
} from '@/services/swapService';

export const useSwapRequestsData = (
  memberName: string | undefined | 'admin',
  isEnabled: boolean
) => {
  const isAdminView = memberName === 'admin';
  
  // Buscar solicitações de trocas (enviadas e recebidas) - apenas para membros específicos
  const swapRequests = useQuery({
    queryKey: ['member-swap-requests', memberName],
    queryFn: async () => {
      if (!memberName || memberName === 'admin') return [];
      
      console.log('Fetching swap requests for member:', memberName);
      
      await supabase.rpc('set_current_member', { member_name: memberName });
      
      const { data, error } = await supabase
        .from('solicitacoes_troca')
        .select('*')
        .or(`id_solicitante.eq.${memberName},membro_destino.eq.${memberName}`)
        .eq('status', 'pendente')
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching swap requests:', error);
        return [];
      }

      // Validar e converter os dados para o tipo SwapRequest
      return (data || []).map(item => ({
        id: item.id,
        id_solicitante: item.id_solicitante,
        data_solicitacao: item.data_solicitacao,
        mensagem: item.mensagem || '',
        status: item.status === 'pendente' ||
                item.status === 'aguardando_candidatos' ||
                item.status === 'aguardando_aprovacao' ||
                item.status === 'aceita' ||
                item.status === 'rejeitada' ||
                item.status === 'cancelada'
                ? item.status
                : 'pendente', // Default para pendente se inválido
        created_at: item.created_at,
        updated_at: item.updated_at,
        data_resposta: item.data_resposta || undefined,
        id_escala_origem: item.id_escala_origem,
        id_escala_destino: item.id_escala_destino || undefined,
        membro_destino: item.membro_destino || undefined,
        tipo_solicitacao: item.tipo_solicitacao || 'troca',
        candidato_selecionado: item.candidato_selecionado || undefined,
        data_selecao_candidato: item.data_selecao_candidato || undefined,
        observacoes_admin: item.observacoes_admin || undefined,
        responsavel_aprovacao: item.responsavel_aprovacao || undefined
      }));
    },
    enabled: isEnabled && !!memberName && memberName !== 'admin'
  });

  // Buscar contagem total de todas as solicitações (para admin)
  const totalSwapRequestsCount = useQuery({
    queryKey: ['total-swap-requests-count'],
    queryFn: async () => {
      console.log('Fetching total swap requests count for admin...');
      return await fetchAllSwapRequestsCount();
    },
    enabled: isEnabled && isAdminView,
  });

  // Buscar contagem de trocas com candidatos (prontas para aprovação) - apenas para admin
  const swapsWithCandidates = useQuery({
    queryKey: ['swaps-with-candidates'],
    queryFn: async () => {
      console.log('Fetching swaps with candidates for admin...');
      return await fetchSwapsWithCandidates();
    },
    enabled: isEnabled && isAdminView,
  });

  // Buscar contagem de trocas aguardando candidatos - apenas para admin
  const swapsAwaitingCandidates = useQuery({
    queryKey: ['swaps-awaiting-candidates'],
    queryFn: fetchSwapsAwaitingCandidates,
    enabled: isEnabled && isAdminView,
  });

  // Buscar contagem de trocas aprovadas - apenas para admin
  const approvedSwapsCount = useQuery({
    queryKey: ['approved-swaps-count'],
    queryFn: fetchApprovedSwapsCount,
    enabled: isEnabled && isAdminView,
  });

  return {
    swapRequests: swapRequests.data,
    totalSwapRequestsCount: totalSwapRequestsCount.data,
    swapsWithCandidatesCount: swapsWithCandidates.data,
    swapsAwaitingCandidatesCount: swapsAwaitingCandidates.data,
    approvedSwapsCount: approvedSwapsCount.data
  };
};
