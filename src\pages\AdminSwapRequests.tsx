
import React, { useState } from 'react';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Users, CheckCircle, Clock, XCircle } from 'lucide-react';
import { useSwapRequests } from '@/hooks/useSwapRequests';
import { CollapsibleAdminSwapCard } from '@/components/swaps/CollapsibleAdminSwapCard';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { useQueryClient } from '@tanstack/react-query';

const AdminSwapRequests = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [openCardId, setOpenCardId] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const {
    allSwapRequests,
    isLoading,
    swapsWithCandidatesCount,
    swapsAwaitingCandidatesCount,
    approvedSwapsCount
  } = useSwapRequests();

  const handleRequestUpdate = () => {
    setRefreshTrigger(prev => prev + 1);
    // Invalidar todas as queries relacionadas para garantir atualização
    queryClient.invalidateQueries({ queryKey: ['all-swap-requests-with-schedule'] });
    queryClient.invalidateQueries({ queryKey: ['swaps-with-candidates-count'] });
    queryClient.invalidateQueries({ queryKey: ['swaps-awaiting-candidates-count'] });
    queryClient.invalidateQueries({ queryKey: ['approved-swaps-count'] });
  };

  const handleCardToggle = (cardId: string) => {
    setOpenCardId(openCardId === cardId ? null : cardId);
  };

  // Filtrar solicitações por status - CORRIGIDO
  const pendingRequests = allSwapRequests?.filter(req => 
    req.status === 'aguardando_aprovacao' // Pendentes = com candidatos aguardando aprovação
  ) || [];
  
  const waitingRequests = allSwapRequests?.filter(req => 
    req.status === 'aguardando_candidatos' // Aguardando = sem candidatos
  ) || [];
  
  const approvedRequests = allSwapRequests?.filter(req => req.status === 'aceita') || [];
  const rejectedRequests = allSwapRequests?.filter(req => req.status === 'rejeitada') || [];

  if (isLoading) {
    return (
      <SidebarProvider>
        <div className="min-h-screen flex w-full">
          <AppSidebar />
          <SidebarInset>
            <div className="flex items-center justify-center h-64">
              <RefreshCw className="w-8 h-8 animate-spin text-orange-500" />
              <span className="ml-2 text-slate-600">Carregando solicitações...</span>
            </div>
          </SidebarInset>
        </div>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <SidebarInset>
          <div className="flex flex-col">
            {/* Header com sidebar trigger */}
            <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
              <SidebarTrigger className="-ml-1" />
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Gerenciar Solicitações de Troca</h1>
                <p className="text-slate-600">Visualize e gerencie todas as solicitações de troca de escalas</p>
              </div>
            </header>

            {/* Content */}
            <div className="flex-1 p-6 space-y-6">
              {/* Stats Cards - CORRIGIDOS */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card className="border-slate-200">
                  <CardContent className="p-4">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Clock className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-2xl font-bold text-slate-900">{pendingRequests.length}</p>
                        <p className="text-sm text-slate-600">Pendentes</p>
                        <p className="text-xs text-slate-500">Com candidatos</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-slate-200">
                  <CardContent className="p-4">
                    <div className="flex items-center">
                      <div className="p-2 bg-amber-100 rounded-lg">
                        <Users className="w-5 h-5 text-amber-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-2xl font-bold text-slate-900">{waitingRequests.length}</p>
                        <p className="text-sm text-slate-600">Aguardando</p>
                        <p className="text-xs text-slate-500">Sem candidatos</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-slate-200">
                  <CardContent className="p-4">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-2xl font-bold text-slate-900">{approvedRequests.length}</p>
                        <p className="text-sm text-slate-600">Aprovadas</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-slate-200">
                  <CardContent className="p-4">
                    <div className="flex items-center">
                      <div className="p-2 bg-red-100 rounded-lg">
                        <XCircle className="w-5 h-5 text-red-600" />
                      </div>
                      <div className="ml-3">
                        <p className="text-2xl font-bold text-slate-900">{rejectedRequests.length}</p>
                        <p className="text-sm text-slate-600">Rejeitadas</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Tabs */}
              <Tabs defaultValue="pending" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="pending" className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    Pendentes
                    {pendingRequests.length > 0 && (
                      <Badge variant="secondary" className="bg-blue-100 text-blue-800 ml-1">
                        {pendingRequests.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="waiting" className="flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    Aguardando
                    {waitingRequests.length > 0 && (
                      <Badge variant="secondary" className="bg-amber-100 text-amber-800 ml-1">
                        {waitingRequests.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="approved" className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4" />
                    Aprovadas
                    {approvedRequests.length > 0 && (
                      <Badge variant="secondary" className="bg-green-100 text-green-800 ml-1">
                        {approvedRequests.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="rejected" className="flex items-center gap-2">
                    <XCircle className="w-4 h-4" />
                    Rejeitadas
                    {rejectedRequests.length > 0 && (
                      <Badge variant="secondary" className="bg-red-100 text-red-800 ml-1">
                        {rejectedRequests.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="pending" className="space-y-3">
                  {pendingRequests.length === 0 ? (
                    <Card className="border-slate-200">
                      <CardContent className="p-8 text-center">
                        <Clock className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-slate-900 mb-2">Nenhuma solicitação pendente</h3>
                        <p className="text-slate-600">Não há solicitações com candidatos aguardando aprovação.</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="space-y-2">
                      {pendingRequests.map((request) => (
                        <CollapsibleAdminSwapCard
                          key={request.id}
                          request={request}
                          onRequestUpdate={handleRequestUpdate}
                          isOpen={openCardId === request.id}
                          onToggle={() => handleCardToggle(request.id)}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="waiting" className="space-y-3">
                  {waitingRequests.length === 0 ? (
                    <Card className="border-slate-200">
                      <CardContent className="p-8 text-center">
                        <Users className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-slate-900 mb-2">Nenhuma solicitação aguardando</h3>
                        <p className="text-slate-600">Não há solicitações aguardando candidatos.</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="space-y-2">
                      {waitingRequests.map((request) => (
                        <CollapsibleAdminSwapCard
                          key={request.id}
                          request={request}
                          onRequestUpdate={handleRequestUpdate}
                          isOpen={openCardId === request.id}
                          onToggle={() => handleCardToggle(request.id)}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="approved" className="space-y-3">
                  {approvedRequests.length === 0 ? (
                    <Card className="border-slate-200">
                      <CardContent className="p-8 text-center">
                        <CheckCircle className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-slate-900 mb-2">Nenhuma solicitação aprovada</h3>
                        <p className="text-slate-600">As solicitações aprovadas aparecerão aqui.</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="space-y-2">
                      {approvedRequests.map((request) => (
                        <CollapsibleAdminSwapCard
                          key={request.id}
                          request={request}
                          onRequestUpdate={handleRequestUpdate}
                          isOpen={openCardId === request.id}
                          onToggle={() => handleCardToggle(request.id)}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="rejected" className="space-y-3">
                  {rejectedRequests.length === 0 ? (
                    <Card className="border-slate-200">
                      <CardContent className="p-8 text-center">
                        <XCircle className="w-12 h-12 text-slate-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-slate-900 mb-2">Nenhuma solicitação rejeitada</h3>
                        <p className="text-slate-600">As solicitações rejeitadas aparecerão aqui.</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className="space-y-2">
                      {rejectedRequests.map((request) => (
                        <CollapsibleAdminSwapCard
                          key={request.id}
                          request={request}
                          onRequestUpdate={handleRequestUpdate}
                          isOpen={openCardId === request.id}
                          onToggle={() => handleCardToggle(request.id)}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default AdminSwapRequests;
