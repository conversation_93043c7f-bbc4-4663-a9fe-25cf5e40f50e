
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, differenceInCalendarDays } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { DayOfWeek } from '@/types';

// Get all days in a month, padded with days from previous and next months to fill a grid
export const getPaddedCalendarDays = (year: number, month: number): (Date | null)[] => {
  const firstDayOfMonth = startOfMonth(new Date(year, month));
  const lastDayOfMonth = endOfMonth(new Date(year, month));
  const startDate = startOfWeek(firstDayOfMonth, { weekStartsOn: 0 }); // 0 = Sunday (updated from 1)
  const endDate = endOfWeek(lastDayOfMonth, { weekStartsOn: 0 }); // 0 = Sunday (updated from 1)
  
  const days: (Date | null)[] = [];
  let currentDate = startDate;
  
  while (currentDate <= endDate) {
    days.push(currentDate);
    currentDate = addDays(currentDate, 1);
  }
  
  return days;
};

// Format month for display
export const formatMonth = (date: Date): string => {
  return format(date, 'MMMM yyyy', { locale: ptBR }).replace(/^\w/, (c) => c.toUpperCase());
};

// Check if a date is today
export const isToday = (date: Date): boolean => {
  return isSameDay(date, new Date());
};

// Check if two dates are the same
export const isSameDate = (date1: Date | null, date2: Date | null): boolean => {
  if (!date1 || !date2) return false;
  return format(date1, 'yyyy-MM-dd') === format(date2, 'yyyy-MM-dd');
};

// Get day of the week in Portuguese
export const getDayOfWeek = (date: Date): DayOfWeek => {
  const dayOfWeek = format(date, 'EEEE', { locale: ptBR });
  
  switch (dayOfWeek.toLowerCase()) {
    case 'domingo':
      return 'Domingo';
    case 'segunda-feira':
      return 'Segunda-Feira';
    case 'terça-feira':
      return 'Terça-Feira';
    case 'quarta-feira':
      return 'Quarta-Feira';
    case 'quinta-feira':
      return 'Quinta-Feira';
    case 'sexta-feira':
      return 'Sexta-Feira';
    case 'sábado':
      return 'Sabado';
    default:
      return 'Domingo';
  }
};

// Get the background color for a shift based on its name
export const getShiftBackgroundColor = (shiftName: string): string => {
  switch (shiftName) {
    case 'Manhã':
    case 'EBD':
      return 'bg-blue-50';
    case 'Tarde':
    case '1º Culto':
      return 'bg-amber-50';
    case 'Noite':
    case '2º Culto':
      return 'bg-green-50';
    default:
      return 'bg-gray-50';
  }
};

// Get shift times based on shift name
export const getShiftTimes = (shiftName: string): { startTime: string; endTime: string } => {
  switch (shiftName) {
    case 'Manhã':
    case 'EBD':
      return { startTime: '08:00', endTime: '12:00' };
    case 'Tarde':
    case '1º Culto':
      return { startTime: '12:00', endTime: '18:00' };
    case 'Noite':
    case '2º Culto':
      return { startTime: '18:00', endTime: '22:00' };
    default:
      return { startTime: '00:00', endTime: '00:00' };
  }
};

// Check if a person was scheduled recently in any schedule
export const wasPersonScheduledRecently = (personId: string, currentDate: Date, schedules: Record<string, any>): boolean => {
  for (const dateKey in schedules) {
    const scheduleDate = schedules[dateKey].date;
    
    // Skip if the date is the same as current date
    if (format(scheduleDate, "yyyy-MM-dd") === format(currentDate, "yyyy-MM-dd")) continue;
    
    const daysDifference = Math.abs(differenceInCalendarDays(scheduleDate, currentDate));
    
    // If the dates are close (less than 21 days apart)
    if (daysDifference < 21) {
      const daySchedule = schedules[dateKey];
      // Check if person is assigned on this day
      for (const shiftKey in daySchedule.shifts) {
        if (daySchedule.shifts[shiftKey].includes(personId)) {
          return true;
        }
      }
    }
  }
  
  return false;
};
