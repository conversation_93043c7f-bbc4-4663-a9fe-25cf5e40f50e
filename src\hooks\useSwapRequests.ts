
import { useSwapRequestsQueries } from './useSwapRequestsQueries';
import { useSwapRequestsMutations } from './useSwapRequestsMutations';
import { useSwapRequestsHelpers } from './useSwapRequestsHelpers';

export const useSwapRequests = () => {
  const queriesData = useSwapRequestsQueries();
  const mutationsData = useSwapRequestsMutations(queriesData.memberSchedules);
  const helpersData = useSwapRequestsHelpers();

  return {
    ...queriesData,
    ...mutationsData,
    ...helpersData,
    // Map the properties to what AdminSwapRequests expects
    allSwapRequests: queriesData.allSwapRequestsWithSchedule,
    isLoading: queriesData.loading,
    // Use real counts from the queries
    swapsWithCandidatesCount: queriesData.swapsWithCandidatesCount,
    swapsAwaitingCandidatesCount: queriesData.swapsAwaitingCandidatesCount,
    approvedSwapsCount: queriesData.approvedSwapsCount,
  };
};
