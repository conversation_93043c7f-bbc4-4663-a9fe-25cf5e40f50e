import React, { useState, useEffect, useRef } from 'react';
import * as htmlToImage from 'html-to-image';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useSchedule } from '@/contexts/ScheduleContext';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight, Calendar, ChevronDown, ChevronUp, Users, Image, X } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'sonner';
import { format, parse } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { parseDateSafely } from '@/utils/scheduleUtils';
import { Loading } from '@/components/ui/loading';
import { Skeleton } from '@/components/ui/skeleton';
import { supabase } from '@/integrations/supabase/client';

// Interface for Schedule object structure
interface Schedule {
  id: string;
  title: string;
  date: string;
  days: Date[];
  createdAt: string;
  createdBy: string;
  status: 'active' | 'archived';
  scheduleData?: any[]; // For storing the actual schedule data
  calendarImage?: string; // Base64 image of the calendar
}

interface ScheduleDetailProps {
  schedule: Schedule;
  onClose: () => void;
}

const ScheduleDetail: React.FC<ScheduleDetailProps> = ({ schedule, onClose }) => {
  const [expanded, setExpanded] = useState<Record<string, boolean>>({});
  const [showLargeImage, setShowLargeImage] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const tableRef = useRef<HTMLDivElement>(null);

  const generateCalendarImage = async () => {
    if (!tableRef.current || !schedule.scheduleData) return;
    
    setIsGeneratingImage(true);
    try {
      const dataUrl = await htmlToImage.toPng(tableRef.current, {
        quality: 1,
        pixelRatio: 2,
        backgroundColor: '#ffffff'
      });

      // Atualiza o schedule com a nova imagem
      schedule.calendarImage = dataUrl;
      toast.success('Imagem da escala gerada com sucesso!');
    } catch (error) {
      console.error('Erro ao gerar imagem:', error);
      toast.error('Falha ao gerar imagem da escala');
    } finally {
      setIsGeneratingImage(false);
    }
  };

  if (!schedule) return null;

  const toggleExpand = (sectionId: string) => {
    setExpanded(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    } catch (e) {
      return dateString;
    }
  };

  return (
    <div className="space-y-4 max-h-[70vh] overflow-y-auto p-2">
      <div className="mb-4">
        <h3 className="text-lg font-medium">{schedule.title}</h3>
        <p className="text-sm text-muted-foreground">Criada em: {formatDate(schedule.createdAt)}</p>
        <p className="text-sm text-muted-foreground">Por: {schedule.createdBy}</p>
      </div>

      {schedule.scheduleData && (
        <div className="schedule-grid border-2 border-gray-300 rounded-lg p-4 mb-4 bg-white" ref={tableRef}>
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse">
              <thead className="bg-gray-100">
                <tr>
                  <th colSpan={5} className="border-b-2 border-gray-300 py-3 text-center">
                    <div className="flex justify-center items-center gap-4">
                      <img src="/logo1.png" alt="Logo 1" className="h-12" />
                      <h3 className="text-xl font-bold text-gray-800">ESCALA DE SERVIÇO</h3>
                      <img src="/logo2.png" alt="Logo 2" className="h-12" />
                    </div>
                  </th>
                </tr>
                
              </thead>
              <tbody className="bg-white schedule-grid">
                {Object.values(
                  schedule.scheduleData.reduce<Record<string, {
                    Dia: string;
                    Data: string;
                    Turno: string;
                    Membros: Array<{ nome: string; isLider: boolean }>;
                  }>>((acc, shift) => {
                    const key = `${shift.Dia}-${shift.Data}-${shift.Turno}`;
                    if (!acc[key]) {
                      acc[key] = {
                        Dia: shift.Dia,
                        Data: shift.Data,
                        Turno: shift.Turno,
                        Membros: [
                          { nome: shift.Lider, isLider: true },
                          ...['Membro1', 'Membro2', 'Membro3', 'Membro4']
                            .map(m => shift[m])
                            .filter(Boolean)
                            .map(nome => ({ nome, isLider: false }))
                        ]
                      };
                    }
                    return acc;
                  }, {})
                ).map((group, idx) => (
                  <React.Fragment key={idx}>
                    <tr className="border-b border-gray-200 schedule-day-column">
                      <td className="px-4 py-3 font-medium text-sm text-gray-800 schedule-day-column">{group.Dia}</td>
                      <td className="px-4 py-3 text-sm text-gray-800 schedule-day-column">{group.Data}</td>
                      <td className="px-4 py-3 text-sm text-gray-800 schedule-day-column">{group.Turno}</td>
                      <td className="px-4 py-3 schedule-day-column" colSpan={2}>
                        <div className="flex flex-col gap-1 schedule-day-column">
                          {group.Membros.map((membro, i) => (
                            <span
                              key={i}
                              className={`px-2 py-1 rounded ${membro.isLider ? 'bg-orange-100 font-medium' : ''}`}
                            >
                              {membro.nome}
                            </span>
                          ))}
                        </div>
                      </td>
                    </tr>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
          {!schedule.calendarImage && (
            <div className="mt-4 flex justify-end">
              <Button
                onClick={generateCalendarImage}
                disabled={isGeneratingImage}
              >
                {isGeneratingImage ? 'Gerando Imagem...' : 'Gerar Imagem da Escala'}
              </Button>
            </div>
          )}
        </div>
      )}

      {schedule.calendarImage && (
        <div className="border rounded-md p-4">
          <div
            className="flex justify-between items-center cursor-pointer"
            onClick={() => toggleExpand('image')}
          >
            <div className="flex items-center">
              <Image className="h-5 w-5 mr-2 text-primary" />
              <h4 className="font-medium">Visualizar imagem da escala</h4>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  const link = document.createElement('a');
                  link.href = schedule.calendarImage;
                  link.download = `escala-${schedule.title.replace(/\s+/g, '-').toLowerCase()}.png`;
                  link.click();
                }}
              >
                Baixar
              </Button>
              {expanded['image'] ? <ChevronUp /> : <ChevronDown />}
            </div>
          </div>
          
          {expanded['image'] && (
            <div className="mt-2 pl-7 text-center">
              <div className="relative">
                <img
                  src={schedule.calendarImage}
                  alt="Imagem da Escala"
                  className={`max-w-full h-auto border rounded-md shadow-sm ${showLargeImage ? 'cursor-zoom-out' : 'cursor-zoom-in'}`}
                  onClick={() => setShowLargeImage(!showLargeImage)}
                />
                {showLargeImage && (
                  <div
                    className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center"
                    onClick={() => setShowLargeImage(false)}
                    style={{ top: 0, left: 0, right: 0, bottom: 0 }}
                  >
                    <div className="relative max-w-full max-h-full overflow-auto">
                      <img
                        src={schedule.calendarImage}
                        alt="Imagem da Escala"
                        className="max-w-full max-h-screen h-auto object-contain"
                      />
                      <div className="absolute top-4 right-4 flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            const link = document.createElement('a');
                            link.href = schedule.calendarImage;
                            link.download = `escala-${schedule.title.replace(/\s+/g, '-').toLowerCase()}.png`;
                            link.click();
                          }}
                        >
                          Baixar Imagem
                        </Button>
                        <button
                          className="bg-white/80 text-black p-2 rounded-full hover:bg-white"
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowLargeImage(false);
                          }}
                        >
                          <X className="h-6 w-6" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      <div className="border rounded-md p-4">
        <div 
          className="flex justify-between items-center cursor-pointer" 
          onClick={() => toggleExpand('days')}
        >
          <div className="flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-primary" />
            <h4 className="font-medium">Dias selecionados</h4>
          </div>
          {expanded['days'] ? <ChevronUp /> : <ChevronDown />}
        </div>
        
        {expanded['days'] && (
          <div className="mt-2 pl-7 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
            {schedule.days?.map((day, idx) => (
              <div key={idx} className="text-sm p-1 bg-muted rounded">
                {formatDate(day.toString())}
              </div>
            ))}
          </div>
        )}
      </div>

      {schedule.scheduleData && schedule.scheduleData.length > 0 && (
        <div className="border rounded-md p-4">
          <div 
            className="flex justify-between items-center cursor-pointer" 
            onClick={() => toggleExpand('shifts')}
          >
            <div className="flex items-center">
              <Users className="h-5 w-5 mr-2 text-primary" />
              <h4 className="font-medium">Pessoas escaladas</h4>
            </div>
            {expanded['shifts'] ? <ChevronUp /> : <ChevronDown />}
          </div>
          
          {expanded['shifts'] && (
            <div className="mt-2 pl-7">
              {schedule.scheduleData.map((shift, idx) => (
                <div key={idx} className="mb-2 pb-2 border-b last:border-0">
                  <p className="font-medium">{shift.Dia} - {shift.Data} - {shift.Turno}</p>
                  <div className="mt-1 grid grid-cols-1 sm:grid-cols-2 gap-x-4 text-sm">
                    {shift.Lider && <p>Líder: {shift.Lider}</p>}
                    {shift.Membro1 && <p>Membro 1: {shift.Membro1}</p>}
                    {shift.Membro2 && <p>Membro 2: {shift.Membro2}</p>}
                    {shift.Membro3 && <p>Membro 3: {shift.Membro3}</p>}
                    {shift.Membro4 && <p>Membro 4: {shift.Membro4}</p>}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className="flex justify-end mt-4 pt-2 border-t">
        <Button variant="outline" onClick={onClose}>Fechar</Button>
      </div>
    </div>
  );
};

const PastSchedules = () => {
  const navigate = useNavigate();
  const { auth } = useAuth();
  const { scheduleState, clearSchedule, toggleDaySelection, addPersonToShift } = useSchedule();
  const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null);
  const [schedules, setSchedules] = useState<Schedule[]>([]);
  const [filteredSchedules, setFilteredSchedules] = useState<Schedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [detailDialog, setDetailDialog] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState("all");
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());
  const [editHistory, setEditHistory] = useState<Schedule[][]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [pageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const authCheckCompleted = useRef(false);
  const dataFetchedRef = useRef(false);
  
  // Sample months for filter
  const months = [
    { value: 'all', label: 'Todos os meses' },
    { value: '0', label: 'Janeiro' },
    { value: '1', label: 'Fevereiro' },
    { value: '2', label: 'Março' },
    { value: '3', label: 'Abril' },
    { value: '4', label: 'Maio' },
    { value: '5', label: 'Junho' },
    { value: '6', label: 'Julho' },
    { value: '7', label: 'Agosto' },
    { value: '8', label: 'Setembro' },
    { value: '9', label: 'Outubro' },
    { value: '10', label: 'Novembro' },
    { value: '11', label: 'Dezembro' }
  ];
  
  const years = [
    { value: '2023', label: '2023' },
    { value: '2024', label: '2024' },
    { value: '2025', label: '2025' }
  ];

  // Transform data from escalasPassadas table to Schedule format - GROUP BY MONTH
  const transformEscalasToSchedules = (escalasData: any[]): Schedule[] => {
    if (!escalasData || escalasData.length === 0) return [];

    // Group by month
    const grouped = new Map<string, any[]>();
    
    escalasData.forEach(escala => {
      if (!escala.Data) return;
      
      // Parse date from DD/MM/YYYY format
      const dateParts = escala.Data.split('/');
      if (dateParts.length !== 3) return;
      
    const date = new Date(
      parseInt(dateParts[2]), // year
      parseInt(dateParts[1]) - 1, // month (0-indexed)
      parseInt(dateParts[0]) // day
    );
    
    if (isNaN(date.getTime())) return;
      
      // Create month key based on year and month
      const year = date.getFullYear();
      const month = date.getMonth();
      const monthKey = `${year}-${month}`;
      
      if (!grouped.has(monthKey)) {
        grouped.set(monthKey, []);
      }
      grouped.get(monthKey)!.push(escala);
    });

    // Transform groups to Schedule objects
    const schedules: Schedule[] = [];
    
    grouped.forEach((escalas, monthKey) => {
      if (escalas.length === 0) return;
      
      // Sort escalas by date
      escalas.sort((a, b) => {
        const dateA = parseEscalaDate(a.Data);
        const dateB = parseEscalaDate(b.Data);
        return dateA.getTime() - dateB.getTime();
      });
      
      const firstEscala = escalas[0];
      const firstDate = parseEscalaDate(firstEscala.Data);
      
      // Get unique dates for the entire month
      const uniqueDates = [...new Set(escalas.map(e => e.Data))]
        .map(dateStr => parseEscalaDate(dateStr))
        .filter(date => !isNaN(date.getTime()))
        .sort((a, b) => a.getTime() - b.getTime());
      
      const monthName = format(firstDate, 'MMMM yyyy', { locale: ptBR });
      
      const schedule: Schedule = {
        id: `escala-month-${monthKey}`,
        title: `Escala de ${monthName}`,
        date: firstDate.toISOString(),
        days: uniqueDates,
        createdAt: firstDate.toISOString(),
        createdBy: 'Sistema',
        status: 'active',
        scheduleData: escalas
      };
      
      schedules.push(schedule);
    });
    
    return schedules.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  };

  // Helper function to parse date from DD/MM/YYYY format
  const parseEscalaDate = (dateString: string): Date => {
    if (!dateString) return new Date();
    
    const parts = dateString.split('/');
    if (parts.length !== 3) return new Date();
    
    return new Date(
      parseInt(parts[2]), // year
      parseInt(parts[1]) - 1, // month (0-indexed)
      parseInt(parts[0]) // day
    );
  };

  // Get paginated data
  const getPaginatedData = () => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredSchedules.slice(startIndex, endIndex);
  };
  
  // Check authentication with improved persistence
  useEffect(() => {
    const checkAuth = () => {
      if (!auth.isAuthenticated) {
        // Check localStorage before redirecting
        const hasToken = localStorage.getItem('auth_token');
        if (!hasToken && !authCheckCompleted.current) {
          authCheckCompleted.current = true;
          navigate('/login', { replace: true });
        }
      } else {
        authCheckCompleted.current = true;
      }
    };
    
    checkAuth();
    
    // Add event listener to check auth on window focus
    window.addEventListener('focus', checkAuth);
    
    return () => {
      window.removeEventListener('focus', checkAuth);
    };
  }, [auth.isAuthenticated, navigate]);
  
  // Fetch schedules from Supabase escalasPassadas table
  useEffect(() => {
    if ((auth.isAuthenticated || localStorage.getItem('auth_token')) && !dataFetchedRef.current) {
      dataFetchedRef.current = true;
      fetchSchedules();
    }
  }, [auth.isAuthenticated]);
  
  // Function to fetch schedules from Supabase
  const fetchSchedules = async () => {
    setLoading(true);
    try {
      console.log('Fetching escalas passadas from Supabase...');
      
      // Fetch data from escalasPassadas table
      const { data: escalasData, error } = await supabase
        .from('escalasPassadas')
        .select('*')
        .order('Data', { ascending: false });

      if (error) {
        console.error('Error fetching escalas passadas:', error);
        throw error;
      }

      console.log('Escalas fetched:', escalasData?.length || 0);

      // Transform the data to Schedule format (grouped by month)
      const transformedSchedules = transformEscalasToSchedules(escalasData || []);
      
      // Also get schedules from localStorage (for locally created ones)
      const localSchedulesStr = localStorage.getItem('saved_schedules');
      let localSchedules: Schedule[] = [];
      
      if (localSchedulesStr) {
        try {
          const parsedSchedules = JSON.parse(localSchedulesStr);
          if (Array.isArray(parsedSchedules)) {
            localSchedules = parsedSchedules.map(schedule => ({
              ...schedule,
              days: Array.isArray(schedule.days) 
                ? schedule.days.map(day => {
                    if (day instanceof Date) {
                      return day;
                    } else if (typeof day === 'string') {
                      return parseDateSafely(day);
                    } else {
                      return new Date();
                    }
                  })
                : []
            }));
          }
        } catch (e) {
          console.error("Error parsing local schedules:", e);
        }
      }
      
      // Combine both sources, with Supabase data taking precedence
      const combinedSchedules = [...transformedSchedules];
      
      // Only add local schedules that don't conflict with Supabase ones
      const supabaseIds = new Set(transformedSchedules.map(s => s.id));
      localSchedules.forEach(schedule => {
        if (!supabaseIds.has(schedule.id)) {
          combinedSchedules.push(schedule);
        }
      });
      
      setSchedules(combinedSchedules);
      setFilteredSchedules(combinedSchedules);
      
      console.log('Total schedules loaded:', combinedSchedules.length);
      
    } catch (error) {
      console.error('Failed to fetch schedules:', error);
      toast.error('Falha ao carregar as escalas anteriores');
      
      // Fallback to localStorage only
      const localSchedulesStr = localStorage.getItem('saved_schedules');
      if (localSchedulesStr) {
        try {
          const parsedSchedules = JSON.parse(localSchedulesStr);
          if (Array.isArray(parsedSchedules)) {
            const processedSchedules = parsedSchedules.map(schedule => ({
              ...schedule,
              days: Array.isArray(schedule.days) 
                ? schedule.days.map(day => {
                    if (day instanceof Date) {
                      return day;
                    } else if (typeof day === 'string') {
                      return parseDateSafely(day);
                    } else {
                      return new Date();
                    }
                  })
                : []
            }));
            setSchedules(processedSchedules);
            setFilteredSchedules(processedSchedules);
          }
        } catch (e) {
          console.error("Error parsing local schedules:", e);
          setSchedules([]);
          setFilteredSchedules([]);
        }
      } else {
        setSchedules([]);
        setFilteredSchedules([]);
      }
    } finally {
      setLoading(false);
    }
  };
  
  // Handle view schedule details
  const handleViewSchedule = (schedule: Schedule) => {
    setSelectedSchedule(schedule);
    setDetailDialog(true);
    toast.info(`Visualizando: ${schedule.title}`);
  };
  
  // Handle edit schedule
  const handleEditSchedule = (schedule: Schedule) => {
    clearSchedule(); // Clear any existing schedule state first
    
    // Load the selected schedule into the schedule context
    if (schedule && Array.isArray(schedule.days) && schedule.days.length > 0) {
      // First, add all days to the schedule
      schedule.days.forEach(day => {
        toggleDaySelection(new Date(day));
      });
      
      // Navigate to calendar page with the schedule data
      navigate('/calendar', { 
        state: { 
          scheduleId: schedule.id,
          scheduleData: schedule
        }, 
        replace: true 
      });
      
      toast.info(`Editando: ${schedule.title}`);
    } else {
      toast.error("Não foi possível carregar esta escala para edição");
    }
  };
  
  // Handle delete confirmation
  const handleConfirmDelete = (schedule: Schedule) => {
    setSelectedSchedule(schedule);
    setConfirmDialog(true);
  };
  
  // Handle actual deletion
  const handleDeleteSchedule = async () => {
    if (!selectedSchedule) return;
    
    try {
      // Check if this is a Supabase-based schedule (starts with 'escala-month-')
      if (selectedSchedule.id.startsWith('escala-month-')) {
        // Delete from escalasPassadas table
        if (selectedSchedule.scheduleData && selectedSchedule.scheduleData.length > 0) {
          const idsToDelete = selectedSchedule.scheduleData.map(escala => escala.ID);
          
          const { error } = await supabase
            .from('escalasPassadas')
            .delete()
            .in('ID', idsToDelete);
          
          if (error) {
            console.error('Error deleting from Supabase:', error);
            throw error;
          }
          
          toast.success(`Escala "${selectedSchedule.title}" removida do banco de dados`);
        }
      } else {
        // Handle localStorage schedules
        const localSchedulesStr = localStorage.getItem('saved_schedules');
        if (localSchedulesStr) {
          try {
            const parsedSchedules = JSON.parse(localSchedulesStr);
            if (Array.isArray(parsedSchedules)) {
              const updatedLocalSchedules = parsedSchedules.filter(
                s => s.id !== selectedSchedule.id
              );
              localStorage.setItem('saved_schedules', JSON.stringify(updatedLocalSchedules));
            }
          } catch (e) {
            console.error("Error updating local schedules:", e);
          }
        }
        toast.success(`Escala "${selectedSchedule.title}" removida do armazenamento local`);
      }
      
      // Add current state to history before deletion
      addToHistory([...filteredSchedules]);
      
      // Filter out the deleted schedule
      const updatedSchedules = schedules.filter(s => s.id !== selectedSchedule.id);
      setSchedules(updatedSchedules);
      setFilteredSchedules(updatedSchedules.filter(schedule => {
        // Apply current filters
        if (selectedYear && selectedMonth !== 'all') {
          const scheduleDate = new Date(schedule.date);
          return scheduleDate.getFullYear().toString() === selectedYear && 
                 scheduleDate.getMonth().toString() === selectedMonth;
        } else if (selectedYear) {
          const scheduleDate = new Date(schedule.date);
          return scheduleDate.getFullYear().toString() === selectedYear;
        }
        return true;
      }));
      
      setConfirmDialog(false);
      setSelectedSchedule(null);
    } catch (error) {
      console.error('Failed to delete schedule:', error);
      toast.error('Falha ao excluir a escala');
    }
  };
  
  // Handle month selection
  const handleMonthChange = (value: string) => {
    setSelectedMonth(value);
    setCurrentPage(1); // Reset to first page
    addToHistory([...filteredSchedules]);
  };
  
  // Handle year selection
  const handleYearChange = (value: string) => {
    setSelectedYear(value);
    setCurrentPage(1); // Reset to first page
    addToHistory([...filteredSchedules]);
  };
  
  // Add current state to history
  const addToHistory = (currentState: Schedule[]) => {
    // If we're not at the end of history, truncate
    if (historyIndex < editHistory.length - 1) {
      const newHistory = editHistory.slice(0, historyIndex + 1);
      setEditHistory([...newHistory, currentState]);
    } else {
      setEditHistory([...editHistory, currentState]);
    }
    setHistoryIndex(historyIndex + 1);
  };
  
  // Handle undo
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setFilteredSchedules([...editHistory[historyIndex - 1]]);
      toast.info('Alteração desfeita');
    }
  };
  
  // Handle redo
  const handleRedo = () => {
    if (historyIndex < editHistory.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setFilteredSchedules([...editHistory[historyIndex + 1]]);
      toast.info('Alteração refeita');
    }
  };
  
  // Format date consistently
  const formatCreatedDate = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    } catch (error) {
      console.error("Error formatting date:", error);
      return dateString;
    }
  };
  
  // Filter schedules based on month and year selection
  useEffect(() => {
    if (!schedules.length) {
      setFilteredSchedules([]);
      return;
    }
    
    let filtered = [...schedules];
    
    // Filter by year
    if (selectedYear) {
      filtered = filtered.filter(schedule => {
        const scheduleDate = new Date(schedule.date);
        return scheduleDate.getFullYear().toString() === selectedYear;
      });
    }
    
    // Filter by month if selected and not 'all'
    if (selectedMonth !== 'all') {
      filtered = filtered.filter(schedule => {
        const scheduleDate = new Date(schedule.date);
        return scheduleDate.getMonth().toString() === selectedMonth;
      });
    }
    
    setFilteredSchedules(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [schedules, selectedMonth, selectedYear]);

  const formatDateRange = (days: Date[]) => {
    if (!days || days.length === 0) return '';
    
    const sorted = [...days].sort((a, b) => a.getTime() - b.getTime());
    const firstDay = format(new Date(sorted[0]), 'dd/MM', { locale: ptBR });
    const lastDay = format(new Date(sorted[sorted.length - 1]), 'dd/MM', { locale: ptBR });
    
    return `${firstDay} - ${lastDay}`;
  };

  // Calculate total pages
  const totalPages = Math.ceil(filteredSchedules.length / pageSize);

  // Check for token in local storage before rendering nothing
  if (!auth.isAuthenticated && !localStorage.getItem('auth_token') && authCheckCompleted.current) {
    return null;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <SidebarInset>
          <div className="container mx-auto p-4 md:p-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold">Escalas Passadas</h1>
                <p className="text-muted-foreground">Visualize e gerencie as escalas anteriores por mês</p>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleUndo}
                  disabled={historyIndex <= 0}
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <span className="text-xs text-muted-foreground">
                  {historyIndex + 1}/{editHistory.length || 1}
                </span>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleRedo}
                  disabled={historyIndex >= editHistory.length - 1}
                >
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Filtros</CardTitle>
                <CardDescription>Filtre as escalas por período (agrupadas por mês)</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-4">
                  <div className="w-full sm:w-auto">
                    <label className="text-sm font-medium block mb-1">Mês</label>
                    <Select value={selectedMonth} onValueChange={handleMonthChange}>
                      <SelectTrigger className="w-full sm:w-[180px]">
                        <SelectValue placeholder="Selecione o mês" />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map(month => (
                          <SelectItem key={month.value} value={month.value}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="w-full sm:w-auto">
                    <label className="text-sm font-medium block mb-1">Ano</label>
                    <Select value={selectedYear} onValueChange={handleYearChange}>
                      <SelectTrigger className="w-full sm:w-[180px]">
                        <SelectValue placeholder="Selecione o ano" />
                      </SelectTrigger>
                      <SelectContent>
                        {years.map(year => (
                          <SelectItem key={year.value} value={year.value}>
                            {year.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button 
                      variant="outline" 
                      onClick={() => {
                        // Reset the dataFetched ref so we can fetch again
                        dataFetchedRef.current = false;
                        fetchSchedules();
                      }}
                      className="h-10"
                    >
                      Atualizar
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {loading ? (
              <Card>
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <Skeleton className="h-8 w-3/4" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-0">
                  <Table className="w-full">
                    <TableHeader>
                      <TableRow className="bg-muted/50">
                        <TableHead className="font-medium">Título</TableHead>
                        <TableHead className="font-medium">Período</TableHead>
                        <TableHead className="font-medium">Criado em</TableHead>
                        <TableHead className="font-medium">Status</TableHead>
                        <TableHead className="font-medium text-right">Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {getPaginatedData().length > 0 ? (
                        getPaginatedData().map((schedule) => (
                          <TableRow 
                            key={schedule.id} 
                            className="hover:bg-muted/50 cursor-pointer"
                            onClick={() => handleViewSchedule(schedule)}
                          >
                            <TableCell className="font-medium">{schedule.title}</TableCell>
                            <TableCell>{formatDateRange(schedule.days)}</TableCell>
                            <TableCell>
                              {formatCreatedDate(schedule.createdAt)}
                            </TableCell>
                            <TableCell>
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                schedule.status === 'active' 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }`}>
                                {schedule.status === 'active' ? 'Ativa' : 'Arquivada'}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleEditSchedule(schedule);
                                  }}
                                >
                                  Editar
                                </Button>
                                <Button 
                                  variant="destructive" 
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleConfirmDelete(schedule);
                                  }}
                                >
                                  Excluir
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                            Nenhuma escala encontrada para este período
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
                {totalPages > 1 && (
                  <CardFooter className="flex justify-between py-4">
                    <div className="text-sm text-muted-foreground">
                      Mostrando {(currentPage - 1) * pageSize + 1} a {Math.min(currentPage * pageSize, filteredSchedules.length)} de {filteredSchedules.length} escalas
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
                        disabled={currentPage === 1}
                      >
                        Anterior
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
                        disabled={currentPage === totalPages}
                      >
                        Próximo
                      </Button>
                    </div>
                  </CardFooter>
                )}
              </Card>
            )}
            
            {/* Confirmation Dialog */}
            <AlertDialog open={confirmDialog} onOpenChange={setConfirmDialog}>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                  <AlertDialogDescription>
                    Tem certeza que deseja excluir a escala "{selectedSchedule?.title}"? Esta ação não pode ser desfeita.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancelar</AlertDialogCancel>
                  <AlertDialogAction onClick={handleDeleteSchedule}>
                    Excluir
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            {/* Detail Dialog */}
            <Dialog open={detailDialog} onOpenChange={setDetailDialog}>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Detalhes da Escala</DialogTitle>
                </DialogHeader>
                {selectedSchedule && (
                  <ScheduleDetail 
                    schedule={selectedSchedule} 
                    onClose={() => setDetailDialog(false)} 
                  />
                )}
              </DialogContent>
            </Dialog>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default PastSchedules;
