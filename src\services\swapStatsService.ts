
import { supabase } from '@/integrations/supabase/client';

export async function fetchAllSwapRequestsCount(): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('solicitacoes_troca')
      .select('*', { count: 'exact', head: true });

    if (error) {
      console.error('Error fetching all swap requests count:', error);
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('Failed to fetch all swap requests count:', error);
    return 0;
  }
}

export async function fetchPendingSwapsCount(): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('solicitacoes_troca')
      .select('*', { count: 'exact', head: true })
      .or('status.neq.cancelada,status.neq.concluida,status.neq.vencida');

    if (error) {
      console.error('Error fetching pending swaps count:', error);
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('Failed to fetch pending swaps count:', error);
    return 0;
  }
}

export async function fetchSwapsWithCandidates(): Promise<number> {
  try {
    const { data, error } = await supabase
      .from('solicitacoes_troca')
      .select(`
        id,
        candidaturas_troca!inner(id)
      `)
      .eq('status', 'aguardando_aprovacao');

    if (error) {
      console.error('Error fetching swaps with candidates:', error);
      throw error;
    }

    return data?.length || 0;
  } catch (error) {
    console.error('Failed to fetch swaps with candidates:', error);
    return 0;
  }
}

export async function fetchSwapsAwaitingCandidates(): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('solicitacoes_troca')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'aguardando_candidatos');

    if (error) {
      console.error('Error fetching swaps awaiting candidates:', error);
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('Failed to fetch swaps awaiting candidates:', error);
    return 0;
  }
}

export async function fetchApprovedSwapsCount(): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('solicitacoes_troca')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'aceita');

    if (error) {
      console.error('Error fetching approved swaps count:', error);
      throw error;
    }

    return count || 0;
  } catch (error) {
    console.error('Failed to fetch approved swaps count:', error);
    return 0;
  }
}
