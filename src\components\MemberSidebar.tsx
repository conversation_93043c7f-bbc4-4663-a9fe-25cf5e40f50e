
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { 
  Home, 
  Calendar, 
  Clock, 
  RefreshCw, 
  User, 
  LogOut 
} from 'lucide-react';

const menuItems = [
  {
    title: "Dashboard",
    url: "/membro-dashboard",
    icon: Home,
  },
  {
    title: "Minhas Escalas",
    url: "/membro-escalas",
    icon: Calendar,
  },
  {
    title: "Disponibilidade",
    url: "/membro-disponibilidade", 
    icon: Clock,
  },
  {
    title: "Trocas",
    url: "/membro-trocas",
    icon: RefreshCw,
  },
  {
    title: "Meu Perfil",
    url: "/membro-perfil",
    icon: User,
  },
];

export const MemberSidebar = () => {
  const { auth, logout } = useMemberAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavigation = (url: string) => {
    // Previne qualquer interferência com a sessão durante navegação
    try {
      navigate(url);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  const handleLogout = () => {
    try {
      logout();
      navigate('/membro-login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const getInitials = (name: string) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'M';
  };

  return (
    <Sidebar>
      <SidebarHeader>
        <div className="flex items-center gap-3 p-2">
          <Avatar className="h-10 w-10">
            <AvatarImage src={auth.member?.foto_url} />
            <AvatarFallback>
              {getInitials(auth.member?.NomeCompleto || 'Membro')}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {auth.member?.NomeCompleto || 'Membro'}
            </p>
            <p className="text-xs text-muted-foreground truncate">
              Portal do Membro
            </p>
          </div>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navegação</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton 
                    isActive={location.pathname === item.url}
                    onClick={() => handleNavigation(item.url)}
                  >
                    <item.icon className="w-4 h-4" />
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <Button 
              onClick={handleLogout}
              variant="ghost" 
              className="w-full justify-start"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Sair
            </Button>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
};
