
import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Search, Filter } from 'lucide-react';
import { Member } from '@/types/supabaseTypes';

interface MembersListViewProps {
  members: Member[];
}

export const MembersListView = ({ members }: MembersListViewProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEquipe, setSelectedEquipe] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedCargo, setSelectedCargo] = useState<string>('all');

  // Extrair valores únicos para filtros
  const equipes = useMemo(() => {
    const uniqueEquipes = [...new Set(members.map(m => m.Equipe).filter(Boolean))];
    return uniqueEquipes.sort();
  }, [members]);

  const status = useMemo(() => {
    const uniqueStatus = [...new Set(members.map(m => m.Status).filter(Boolean))];
    return uniqueStatus.sort();
  }, [members]);

  const cargos = useMemo(() => {
    const uniqueCargos = [...new Set(members.map(m => m.CargoMinisterio).filter(Boolean))];
    return uniqueCargos.sort();
  }, [members]);

  // Filtrar membros
  const filteredMembers = useMemo(() => {
    return members.filter(member => {
      const matchesSearch = 
        member.NomeCompleto?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        member['Nome Escala']?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesEquipe = selectedEquipe === 'all' || member.Equipe === selectedEquipe;
      const matchesStatus = selectedStatus === 'all' || member.Status === selectedStatus;
      const matchesCargo = selectedCargo === 'all' || member.CargoMinisterio === selectedCargo;

      return matchesSearch && matchesEquipe && matchesStatus && matchesCargo;
    });
  }, [members, searchTerm, selectedEquipe, selectedStatus, selectedCargo]);

  const getStatusColor = (status: string) => {
    switch (status?.toUpperCase()) {
      case 'ATIVO': return 'bg-green-100 text-green-800';
      case 'INATIVO': return 'bg-red-100 text-red-800';
      case 'DESATIVADO': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  const getInitials = (name: string) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'M';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="w-5 h-5" />
          Lista de Membros
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filtros */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar por nome..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedEquipe} onValueChange={setSelectedEquipe}>
            <SelectTrigger>
              <SelectValue placeholder="Filtrar por equipe" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas as equipes</SelectItem>
              {equipes.map(equipe => (
                <SelectItem key={equipe} value={equipe}>{equipe}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Filtrar por status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os status</SelectItem>
              {status.map(stat => (
                <SelectItem key={stat} value={stat}>{stat}</SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedCargo} onValueChange={setSelectedCargo}>
            <SelectTrigger>
              <SelectValue placeholder="Filtrar por cargo" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todos os cargos</SelectItem>
              {cargos.map(cargo => (
                <SelectItem key={cargo} value={cargo}>{cargo}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Lista de membros */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredMembers.map((member) => (
            <div key={member['Nome Escala']} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={member.foto_url || member['Link Foto']} />
                  <AvatarFallback>
                    {getInitials(member.NomeCompleto || member['Nome Escala'] || '')}
                  </AvatarFallback>
                </Avatar>
                
                <div>
                  <div className="font-medium">{member.NomeCompleto}</div>
                  <div className="text-sm text-gray-500">
                    {member['Nome Escala']} • {member.Telefone}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Badge variant="outline">{member.Equipe}</Badge>
                <Badge variant="outline">{member.CargoMinisterio}</Badge>
                <Badge className={getStatusColor(member.Status || '')}>
                  {member.Status}
                </Badge>
              </div>
            </div>
          ))}
          
          {filteredMembers.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              Nenhum membro encontrado com os filtros aplicados.
            </div>
          )}
        </div>

        <div className="mt-4 text-sm text-gray-500">
          Mostrando {filteredMembers.length} de {members.length} membros
        </div>
      </CardContent>
    </Card>
  );
};
