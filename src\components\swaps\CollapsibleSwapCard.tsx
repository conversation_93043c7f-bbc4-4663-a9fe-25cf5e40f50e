
import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Textarea } from '@/components/ui/textarea';
import { ChevronDown, ChevronUp, Calendar, User, MessageSquare, CheckCircle, XCircle, Clock } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface CollapsibleSwapCardProps {
  request: SwapRequest;
  onRequestUpdate?: () => void;
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'pendente':
    case 'aguardando_candidatos':
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800"><Clock className="w-3 h-3 mr-1" />Aguardando</Badge>;
    case 'aguardando_aprovacao':
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800"><Clock className="w-3 h-3 mr-1" />Aguardando Aprovação</Badge>;
    case 'aceita':
      return <Badge variant="secondary" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Aprovada</Badge>;
    case 'rejeitada':
      return <Badge variant="secondary" className="bg-red-100 text-red-800"><XCircle className="w-3 h-3 mr-1" />Rejeitada</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateString;
  }
};

const getWeekdayName = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', { weekday: 'long' });
  } catch {
    return '';
  }
};

export const CollapsibleSwapCard: React.FC<CollapsibleSwapCardProps> = ({ 
  request, 
  onRequestUpdate 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [observacoes, setObservacoes] = useState('');
  const queryClient = useQueryClient();

  const approveMutation = useMutation({
    mutationFn: async (data: { observacoes?: string }) => {
      const { error } = await supabase
        .from('solicitacoes_troca')
        .update({
          status: 'aceita',
          observacoes_admin: data.observacoes,
          data_resposta: new Date().toISOString(),
        })
        .eq('id', request.id);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Solicitação aprovada com sucesso!');
      queryClient.invalidateQueries({ queryKey: ['all-swap-requests-with-schedule'] });
      onRequestUpdate?.();
    },
    onError: (error) => {
      console.error('Error approving request:', error);
      toast.error('Erro ao aprovar solicitação');
    }
  });

  const rejectMutation = useMutation({
    mutationFn: async (data: { observacoes?: string }) => {
      const { error } = await supabase
        .from('solicitacoes_troca')
        .update({
          status: 'rejeitada',
          observacoes_admin: data.observacoes,
          data_resposta: new Date().toISOString(),
        })
        .eq('id', request.id);

      if (error) throw error;
    },
    onSuccess: () => {
      toast.success('Solicitação rejeitada');
      queryClient.invalidateQueries({ queryKey: ['all-swap-requests-with-schedule'] });
      onRequestUpdate?.();
    },
    onError: (error) => {
      console.error('Error rejecting request:', error);
      toast.error('Erro ao rejeitar solicitação');
    }
  });

  const handleApprove = () => {
    approveMutation.mutate({ observacoes });
  };

  const handleReject = () => {
    rejectMutation.mutate({ observacoes });
  };

  const isPending = request.status === 'aguardando_candidatos' || request.status === 'aguardando_aprovacao';

  return (
    <Card className="w-full border-slate-200 hover:shadow-md transition-shadow">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-slate-50 transition-colors">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
              <div className="flex flex-col gap-2">
                <div className="flex items-center gap-2 flex-wrap">
                  <User className="w-4 h-4 text-slate-600" />
                  <span className="font-medium text-slate-900">{request.id_solicitante}</span>
                  {getStatusBadge(request.status)}
                </div>
                
                {request.scheduleData && (
                  <div className="flex items-center gap-2 text-sm text-slate-600">
                    <Calendar className="w-4 h-4" />
                    <span>
                      {getWeekdayName(request.scheduleData.Data)}, {formatDate(request.scheduleData.Data)}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {request.scheduleData.Turno}
                    </Badge>
                  </div>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <span className="text-xs text-slate-500">
                  {formatDate(request.created_at)}
                </span>
                {isOpen ? (
                  <ChevronUp className="w-4 h-4 text-slate-400" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-slate-400" />
                )}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <CardContent className="pt-0">
            {request.mensagem && (
              <div className="mb-4 p-3 bg-slate-50 rounded-lg">
                <div className="flex items-start gap-2">
                  <MessageSquare className="w-4 h-4 text-slate-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-slate-700 mb-1">Mensagem do solicitante:</p>
                    <p className="text-sm text-slate-600">{request.mensagem}</p>
                  </div>
                </div>
              </div>
            )}

            {request.scheduleData && (
              <div className="mb-4 p-3 border border-slate-200 rounded-lg">
                <h4 className="text-sm font-medium text-slate-700 mb-2">Detalhes da Escala:</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                  <div><strong>Data:</strong> {formatDate(request.scheduleData.Data)}</div>
                  <div><strong>Turno:</strong> {request.scheduleData.Turno}</div>
                  <div><strong>Líder:</strong> {request.scheduleData.Lider}</div>
                  <div><strong>Membros:</strong> {[
                    request.scheduleData.Membro1,
                    request.scheduleData.Membro2,
                    request.scheduleData.Membro3,
                    request.scheduleData.Membro4
                  ].filter(Boolean).join(', ')}</div>
                </div>
              </div>
            )}

            {isPending && (
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-slate-700 block mb-2">
                    Observações do administrador:
                  </label>
                  <Textarea
                    value={observacoes}
                    onChange={(e) => setObservacoes(e.target.value)}
                    placeholder="Digite observações sobre esta decisão (opcional)..."
                    className="min-h-[80px] resize-none"
                  />
                </div>
                
                <div className="flex flex-col gap-2 sm:flex-row sm:gap-2 pt-2">
                  <Button
                    onClick={handleApprove}
                    disabled={approveMutation.isPending}
                    className="bg-green-600 hover:bg-green-700 text-white w-full sm:w-auto sm:flex-1"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    {approveMutation.isPending ? 'Aprovando...' : 'Aprovar'}
                  </Button>
                  
                  <Button
                    onClick={handleReject}
                    disabled={rejectMutation.isPending}
                    variant="destructive"
                    className="w-full sm:w-auto sm:flex-1"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    {rejectMutation.isPending ? 'Rejeitando...' : 'Rejeitar'}
                  </Button>
                </div>
              </div>
            )}

            {request.observacoes_admin && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-sm font-medium text-blue-800 mb-1">Observações do administrador:</p>
                <p className="text-sm text-blue-700">{request.observacoes_admin}</p>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
