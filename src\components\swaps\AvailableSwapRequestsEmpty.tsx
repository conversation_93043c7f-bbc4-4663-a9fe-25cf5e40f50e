
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Hand, AlertCircle } from 'lucide-react';

export const AvailableSwapRequestsEmpty = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Hand className="w-5 h-5" />
          Trocas Disponíveis
        </CardTitle>
        <CardDescription>
          Solicitações de outros membros disponíveis para candidatura
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8">
          <div className="p-4 rounded-full bg-muted/50 w-fit mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-muted-foreground" />
          </div>
          <p className="text-muted-foreground text-lg">Nenhuma troca disponível no momento</p>
          <p className="text-sm text-muted-foreground mt-2">
            Solicitações onde você já se candidatou não são exibidas
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
