
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Calendar, Clock } from 'lucide-react';

interface ProfileSwapHistoryProps {
  swapRequests: any[];
  minhasCandidaturas: any[];
}

export const ProfileSwapHistory = ({ swapRequests, minhasCandidaturas }: ProfileSwapHistoryProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'aceita': return 'bg-green-100 text-green-800';
      case 'rejeitada': return 'bg-red-100 text-red-800';
      case 'aguardando_candidatos': return 'bg-yellow-100 text-yellow-800';
      case 'aguardando_aprovacao': return 'bg-blue-100 text-blue-800';
      case 'selecionada': return 'bg-green-100 text-green-800';
      case 'ativa': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateStr: string) => {
    try {
      return new Date(dateStr).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: 'short',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return dateStr;
    }
  };

  const hasActivity = swapRequests.length > 0 || minhasCandidaturas.length > 0;

  if (!hasActivity) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            Atividade de Trocas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <RefreshCw className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">Nenhuma atividade de troca ainda</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="w-5 h-5" />
          Atividade de Trocas
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 max-h-[300px] overflow-y-auto">
        {swapRequests.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Minhas Solicitações</h4>
            <div className="space-y-2">
              {swapRequests.slice(0, 3).map((request) => (
                <div key={request.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Solicitação de Troca</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {formatDate(request.created_at)}
                      </span>
                    </div>
                  </div>
                  <Badge className={getStatusColor(request.status)}>
                    {request.status.replace('_', ' ')}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {minhasCandidaturas.length > 0 && (
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Minhas Candidaturas</h4>
            <div className="space-y-2">
              {minhasCandidaturas.slice(0, 3).map((candidatura) => (
                <div key={candidatura.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <RefreshCw className="w-4 h-4 text-muted-foreground" />
                      <span className="text-sm font-medium">Candidatura</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        {formatDate(candidatura.data_candidatura)}
                      </span>
                    </div>
                  </div>
                  <Badge className={getStatusColor(candidatura.status)}>
                    {candidatura.status}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
