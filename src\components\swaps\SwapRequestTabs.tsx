import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Calendar, Clock, User, AlertCircle, Check, X, ArrowLeftRight, MessageSquare, Users } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { CandidaturasList } from './CandidaturasList';
import { useCandidaturas } from '@/hooks/useCandidaturas';

interface SwapRequestTabsProps {
  swapRequests: SwapRequest[] | undefined;
  allPendingSwapRequests: SwapRequest[] | undefined;
  onRequestUpdate?: () => void;
  adminView?: boolean;
}

export const SwapRequestTabs = ({
  swapRequests,
  allPendingSwapRequests,
  onRequestUpdate,
  adminView = false
}: SwapRequestTabsProps) => {
  const { auth } = useMemberAuth();
  const { getCandidaturasPorSolicitacao } = useCandidaturas();
  const [processing, setProcessing] = useState<string | null>(null);
  const [selectedRequestForCandidatos, setSelectedRequestForCandidatos] = useState<string | null>(null);

  const memberName = auth.member?.['Nome Escala'];

  // Filtrar solicitações por tipo
  const sentRequests = adminView
    ? swapRequests?.filter(req => req.id_solicitante && req.id_solicitante !== '') || []
    : swapRequests?.filter(req => req.id_solicitante === memberName) || [];
    
  const receivedRequests = adminView
    ? swapRequests?.filter(req => req.membro_destino && req.membro_destino !== '') || []
    : swapRequests?.filter(req => req.membro_destino === memberName) || [];
    
  const allRequests = adminView
    ? swapRequests || []
    : [...(swapRequests || []), ...(allPendingSwapRequests || [])]
        .filter(req => req.id_solicitante !== memberName && req.status === 'pendente')
        .filter(Boolean);

  // Função para substituir membro na escala após aprovação
  const substituirMembroNaEscala = async (solicitacao: SwapRequest) => {
    try {
      // Buscar a escala origem
      const { data: escalaOrigem, error: errorEscala } = await supabase
        .from('escalasPassadas')
        .select('*')
        .eq('ID', solicitacao.id_escala_origem)
        .single();

      if (errorEscala) {
        console.error('Erro ao buscar escala origem:', errorEscala);
        return false;
      }

      if (!escalaOrigem) {
        console.error('Escala origem não encontrada');
        return false;
      }

      // Identificar em qual campo o solicitante está na escala
      let campoParaSubstituir = null;
      const solicitante = solicitacao.id_solicitante;
      
      if (escalaOrigem.Lider === solicitante) {
        campoParaSubstituir = 'Lider';
      } else if (escalaOrigem.Membro1 === solicitante) {
        campoParaSubstituir = 'Membro1';
      } else if (escalaOrigem.Membro2 === solicitante) {
        campoParaSubstituir = 'Membro2';
      } else if (escalaOrigem.Membro3 === solicitante) {
        campoParaSubstituir = 'Membro3';
      } else if (escalaOrigem.Membro4 === solicitante) {
        campoParaSubstituir = 'Membro4';
      }

      if (!campoParaSubstituir) {
        console.error('Solicitante não encontrado na escala');
        return false;
      }

      // Atualizar a escala substituindo o membro
      const updateData = {
        [campoParaSubstituir]: solicitacao.membro_destino
      };

      const { error: updateError } = await supabase
        .from('escalasPassadas')
        .update(updateData)
        .eq('ID', solicitacao.id_escala_origem);

      if (updateError) {
        console.error('Erro ao atualizar escala:', updateError);
        return false;
      }

      console.log(`Membro substituído com sucesso: ${solicitante} → ${solicitacao.membro_destino} no campo ${campoParaSubstituir}`);
      return true;
    } catch (error) {
      console.error('Erro na substituição do membro:', error);
      return false;
    }
  };

  const handleRequestResponse = async (requestId: string, action: 'aceita' | 'rejeitada') => {
    if (!adminView && !memberName) return;

    setProcessing(requestId);
    try {
      const solicitacao = [...(swapRequests || []), ...(allPendingSwapRequests || [])]
        .find(req => req.id === requestId);

      if (!solicitacao) {
        toast.error('Solicitação não encontrada');
        return;
      }

      if (!adminView && memberName) {
        await supabase.rpc('set_current_member', { member_name: memberName });
      }

      const updateData: any = {
        status: action,
        data_resposta: new Date().toISOString()
      };
      
      if (adminView) {
        updateData.responsavel_aprovacao = auth.member?.['Nome Escala'];
      }
      
      const { error } = await supabase
        .from('solicitacoes_troca')
        .update(updateData)
        .eq('id', requestId);

      if (error) {
        console.error('Error updating swap request:', error);
        toast.error('Erro ao responder solicitação: ' + error.message);
        return;
      }

      // Se a solicitação foi aceita, substituir o membro na escala
      if (action === 'aceita' && solicitacao.membro_destino) {
        const substituicaoSucesso = await substituirMembroNaEscala(solicitacao);
        
        if (!substituicaoSucesso) {
          toast.error('Solicitação aprovada, mas houve erro na substituição da escala');
        } else {
          toast.success('Solicitação aprovada e escala atualizada com sucesso!');
        }
      } else {
        if (adminView) {
          toast.success(`Solicitação ${action === 'aceita' ? 'aprovada' : 'rejeitada'} como administrador!`);
        } else {
          toast.success(`Solicitação ${action === 'aceita' ? 'aceita' : 'rejeitada'} com sucesso!`);
        }
      }

      onRequestUpdate?.();
    } catch (error) {
      console.error('Error processing request:', error);
      toast.error('Erro ao processar solicitação');
    } finally {
      setProcessing(null);
    }
  };

  const handleSelectCandidato = async (candidaturaId: string, membroCandidato: string, requestId: string) => {
    try {
      // Atualizar a solicitação com o candidato selecionado
      const { error: updateError } = await supabase
        .from('solicitacoes_troca')
        .update({
          candidato_selecionado: membroCandidato,
          data_selecao_candidato: new Date().toISOString(),
          status: 'aguardando_aprovacao',
          membro_destino: membroCandidato
        })
        .eq('id', requestId);

      if (updateError) {
        throw updateError;
      }

      toast.success('Candidato selecionado! Solicitação aguardando aprovação final.');
      onRequestUpdate?.();
    } catch (error) {
      console.error('Error selecting candidate:', error);
      toast.error('Erro ao selecionar candidato');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pendente':
        return <Badge variant="secondary">Pendente</Badge>;
      case 'aguardando_candidatos':
        return <Badge variant="outline" className="border-blue-500 text-blue-600">Aguardando Candidatos</Badge>;
      case 'aguardando_aprovacao':
        return <Badge variant="outline" className="border-yellow-500 text-yellow-600">Aguardando Aprovação</Badge>;
      case 'aceita':
        return <Badge variant="default">Aceita</Badge>;
      case 'rejeitada':
        return <Badge variant="destructive">Rejeitada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const renderRequestCard = (request: SwapRequest, showActions = false, isAdminView = false) => {
    const candidaturasQuery = getCandidaturasPorSolicitacao(request.id);
    const candidaturas = candidaturasQuery.data || [];
    
    return (
      <div key={request.id} className="p-6 border rounded-lg bg-card hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <Calendar className="w-4 h-4 text-primary" />
            </div>
            <div>
              <span className="font-semibold text-lg">
                {new Date(request.data_solicitacao).toLocaleDateString('pt-BR')}
              </span>
              <p className="text-sm text-muted-foreground">
                {new Date(request.data_solicitacao).toLocaleTimeString('pt-BR', { 
                  hour: '2-digit', 
                  minute: '2-digit' 
                })}
              </p>
            </div>
          </div>
          {getStatusBadge(request.status)}
        </div>
        
        <div className="space-y-3 text-sm">
          <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
            <Clock className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">Tipo:</span>
            <span className="capitalize">{request.tipo_solicitacao}</span>
          </div>
          
          <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
            <ArrowLeftRight className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">
              {isAdminView
                ? `De: ${request.id_solicitante} → Para: ${request.membro_destino || request.candidato_selecionado || 'Qualquer membro'}`
                : request.id_solicitante === memberName
                  ? `Para: ${request.membro_destino || request.candidato_selecionado || 'Qualquer membro'}`
                  : `De: ${request.id_solicitante}`
              }
            </span>
          </div>
          
          {request.candidato_selecionado && (
            <div className="flex items-center gap-3 p-3 bg-green-50 border border-green-200 rounded-lg">
              <User className="w-4 h-4 text-green-600" />
              <span className="font-medium text-green-800">Candidato Selecionado:</span>
              <span className="text-green-700">{request.candidato_selecionado}</span>
            </div>
          )}
          
          {request.mensagem && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm font-medium text-blue-900 mb-1">Motivo:</p>
              <p className="text-blue-800">{request.mensagem}</p>
            </div>
          )}
        </div>

        {/* Botão para ver candidatos (admin) */}
        {isAdminView && (request.status === 'aguardando_candidatos' || request.status === 'aguardando_aprovacao') && candidaturas.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="w-full flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Ver Candidatos ({candidaturas.length})
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Candidatos para a Solicitação</DialogTitle>
                </DialogHeader>
                <CandidaturasList 
                  candidaturas={candidaturas}
                  solicitacaoId={request.id}
                  showActions={request.status === 'aguardando_candidatos'}
                  onSelectCandidato={(candidaturaId, membroCandidato) => 
                    handleSelectCandidato(candidaturaId, membroCandidato, request.id)
                  }
                />
              </DialogContent>
            </Dialog>
          </div>
        )}

        {showActions && (
          <div className="flex gap-3 mt-6 pt-4 border-t">
            {request.status === 'pendente' && (isAdminView || request.id_solicitante !== memberName) && (
              <>
                <Button
                  size="sm"
                  onClick={() => handleRequestResponse(request.id, 'aceita')}
                  disabled={processing === request.id}
                  className="flex items-center gap-2 flex-1"
                >
                  <Check className="w-4 h-4" />
                  {processing === request.id ? 'Processando...' : 'Aceitar Troca'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleRequestResponse(request.id, 'rejeitada')}
                  disabled={processing === request.id}
                  className="flex items-center gap-2 flex-1"
                >
                  <X className="w-4 h-4" />
                  Rejeitar
                </Button>
              </>
            )}
            
            {request.status === 'aguardando_aprovacao' && isAdminView && (
              <>
                <Button
                  size="sm"
                  onClick={() => handleRequestResponse(request.id, 'aceita')}
                  disabled={processing === request.id}
                  className="flex items-center gap-2 flex-1"
                >
                  <Check className="w-4 h-4" />
                  {processing === request.id ? 'Processando...' : 'Aprovar Troca'}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleRequestResponse(request.id, 'rejeitada')}
                  disabled={processing === request.id}
                  className="flex items-center gap-2 flex-1"
                >
                  <X className="w-4 h-4" />
                  Rejeitar Troca
                </Button>
              </>
            )}
          </div>
        )}
      </div>
    );
  };

  const EmptyState = ({ message }: { message: string }) => (
    <div className="text-center py-12">
      <div className="p-4 rounded-full bg-muted/50 w-fit mx-auto mb-4">
        <AlertCircle className="w-8 h-8 text-muted-foreground" />
      </div>
      <p className="text-muted-foreground text-lg">{message}</p>
    </div>
  );

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowLeftRight className="w-5 h-5" />
          {adminView ? 'Gerenciamento de Solicitações' : 'Minhas Solicitações'}
        </CardTitle>
        <CardDescription>
          {adminView
            ? 'Todas as solicitações de troca da equipe'
            : 'Suas solicitações de troca'
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4 max-h-[600px] overflow-y-auto">
          {allRequests.length > 0 ? (
            allRequests.map((request) =>
              renderRequestCard(
                request,
                adminView || request.id_solicitante !== memberName, // Mostrar ações para solicitações de outros membros
                adminView
              )
            )
          ) : (
            <EmptyState message="Nenhuma solicitação encontrada" />
          )}
        </div>
      </CardContent>
    </Card>
  );
};
