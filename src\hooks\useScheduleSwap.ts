
import { useState } from 'react';
import { toast } from 'sonner';

interface PersonSelection {
  id1: string;
  id2: string;
  name1: string;
  name2: string;
  date1: Date;
  date2: Date;
  shift1: string;
  shift2: string;
}

interface UseScheduleSwapParams {
  onSwapConfirm: (
    date1: Date, 
    shift1: string, 
    personId1: string,
    date2: Date, 
    shift2: string, 
    personId2: string
  ) => void;
  saveToHistory: (data: any) => void;
  scheduleState: any;
}

export function useScheduleSwap({ onSwapConfirm, saveToHistory, scheduleState }: UseScheduleSwapParams) {
  const [showSwapDialog, setShowSwapDialog] = useState(false);
  const [selectedPersons, setSelectedPersons] = useState<PersonSelection | null>(null);
  
  const handlePersonClick = (personId: string, personName: string, date: Date, shiftName: string) => {
    if (!personId || !personName) return;
    
    // If no person selected yet, select this one
    if (!selectedPersons) {
      setSelectedPersons({
        id1: personId,
        name1: personName,
        date1: date,
        shift1: shiftName,
        id2: '',
        name2: '',
        date2: new Date(),
        shift2: ''
      });
      toast.info(`Selecionado: ${personName}. Clique em outra pessoa para trocar.`);
    } 
    // If this is the second selection and it's different from the first
    else if (selectedPersons.id1 !== personId || selectedPersons.date1.getTime() !== date.getTime() || selectedPersons.shift1 !== shiftName) {
      setSelectedPersons({
        ...selectedPersons,
        id2: personId,
        name2: personName,
        date2: date,
        shift2: shiftName
      });
      setShowSwapDialog(true);
    } else {
      // Unselect if same person clicked twice
      setSelectedPersons(null);
      toast.info("Seleção cancelada");
    }
  };

  const handleSwapConfirm = () => {
    if (!selectedPersons || !selectedPersons.id1 || !selectedPersons.id2) return;
    
    // Save current state to history before swap
    saveToHistory(JSON.parse(JSON.stringify(scheduleState)));
    
    // Call the swap function
    onSwapConfirm(
      selectedPersons.date1, 
      selectedPersons.shift1, 
      selectedPersons.id1,
      selectedPersons.date2,
      selectedPersons.shift2,
      selectedPersons.id2
    );
    
    toast.success(`Pessoas trocadas com sucesso: ${selectedPersons.name1} e ${selectedPersons.name2}`);
    
    // Reset selected persons
    setSelectedPersons(null);
    setShowSwapDialog(false);
  };

  const handleSwapCancel = () => {
    setSelectedPersons(null);
    setShowSwapDialog(false);
  };
  
  return {
    showSwapDialog,
    setShowSwapDialog,
    selectedPersons,
    handlePersonClick,
    handleSwapConfirm,
    handleSwapCancel
  };
}
