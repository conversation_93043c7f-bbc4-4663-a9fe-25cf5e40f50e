
import React from 'react';
import { <PERSON>, Card<PERSON>eader, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Report, hasIncident } from '@/services/api';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

interface ReportsTableProps {
  reports: Report[];
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onReportClick: (report: Report) => void;
}

const ReportsTable = ({ 
  reports, 
  currentPage, 
  totalPages, 
  onPageChange, 
  onReportClick 
}: ReportsTableProps) => {
  
  // Generate page numbers for pagination
  const getPaginationItems = () => {
    const items = [];
    
    // Always show first page
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink
          isActive={currentPage === 1}
          onClick={() => onPageChange(1)}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );
    
    // Add ellipsis if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Add pages around current page
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      items.push(
        <PaginationItem key={`page-${i}`}>
          <PaginationLink
            isActive={currentPage === i}
            onClick={() => onPageChange(i)}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    // Add ellipsis if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Always show last page if there's more than 1 page
    if (totalPages > 1) {
      items.push(
        <PaginationItem key={`page-${totalPages}`}>
          <PaginationLink
            isActive={currentPage === totalPages}
            onClick={() => onPageChange(totalPages)}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    return items;
  };

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>Relatórios</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="border rounded-md overflow-hidden">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="py-3 px-4 text-left font-medium">Data</th>
                <th className="py-3 px-4 text-left font-medium">Líder</th>
                <th className="py-3 px-4 text-left font-medium hidden md:table-cell">Culto</th>
                <th className="py-3 px-4 text-left font-medium">Detalhes</th>
              </tr>
            </thead>
            <tbody>
              {reports.length > 0 ? (
                reports.map((item) => (
                  <tr 
                    key={item.id} 
                    className={`border-b hover:bg-muted/50 cursor-pointer ${
                      hasIncident(item) ? 'bg-amber-50' : ''
                    }`}
                    onClick={() => {
                      // Only open dialog for reports with incidents
                      if (hasIncident(item)) {
                        onReportClick(item);
                      }
                    }}
                  >
                    <td className="py-3 px-4">{item.data}</td>
                    <td className="py-3 px-4">{item["Nome Lider"]}</td>
                    <td className="py-3 px-4 hidden md:table-cell">{item["Tipo Culto"]}</td>
                    <td className="py-3 px-4">
                      {hasIncident(item) ? (
                        <span className="text-amber-600 font-medium">
                          {item["Alteração ?"]}
                        </span>
                      ) : (
                        <span className="text-green-600">Sem ocorrências</span>
                      )}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="py-4 px-4 text-center text-muted-foreground">
                    Nenhum relatório registrado neste mês
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          
          {/* Pagination */}
          {reports.length > 0 && (
            <div className="flex justify-center p-4 border-t">
              <Pagination>
                <PaginationContent>
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationPrevious onClick={() => onPageChange(currentPage - 1)} />
                    </PaginationItem>
                  )}
                  
                  {getPaginationItems()}
                  
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationNext onClick={() => onPageChange(currentPage + 1)} />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ReportsTable;
