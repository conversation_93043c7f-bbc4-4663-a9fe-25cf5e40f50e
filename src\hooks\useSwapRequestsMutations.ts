
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { ScheduleItem } from '@/types/swapTypes';

export const useSwapRequestsMutations = (memberSchedules?: ScheduleItem[]) => {
  const { auth } = useMemberAuth();
  const queryClient = useQueryClient();

  // Mutação para criar nova solicitação de troca
  const createSwapMutation = useMutation({
    mutationFn: async ({
      scheduleId,
      reason,
    }: {
      scheduleId: string;
      reason: string;
    }) => {
      if (!auth.member?.['Nome Escala']) {
        throw new Error('Membro não autenticado');
      }

      const schedule = memberSchedules?.find((s) => s.ID === scheduleId);
      if (!schedule) {
        throw new Error('Escala selecionada não encontrada');
      }

      await supabase.rpc('set_current_member', { member_name: auth.member['Nome Escala'] });

      // Verificar se já existe solicitação ativa para esta escala
      const { data: existingRequests, error: fetchError } = await supabase
        .from('solicitacoes_troca')
        .select('id, status')
        .eq('id_escala_origem', scheduleId)
        .eq('id_solicitante', auth.member['Nome Escala'])
        .not('status', 'eq', 'cancelada');

      if (fetchError) throw fetchError;
      if (existingRequests && existingRequests.length > 0) {
        throw new Error('Você já tem uma solicitação ativa para esta escala. Cancele a anterior antes de criar uma nova.');
      }

      const { data, error } = await supabase
        .from('solicitacoes_troca')
        .insert({
          id_solicitante: auth.member['Nome Escala'],
          id_escala_origem: scheduleId,
          tipo_solicitacao: 'trocar',
          status: 'aguardando_candidatos',
          mensagem: reason || ''
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      toast.success('Solicitação de troca criada com sucesso!');
      // Invalidar apenas as queries necessárias de forma sequencial
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['swap-requests'] });
        queryClient.invalidateQueries({ queryKey: ['all-pending-swap-requests'] });
        queryClient.invalidateQueries({ queryKey: ['user-all-swap-requests'] });
      }, 100);
    },
    onError: (error: any) => {
      console.error('Error creating swap request:', error);
      toast.error('Erro ao criar solicitação de troca: ' + (error.message || 'Erro desconhecido'));
    },
  });

  // Mutação para cancelar solicitação de troca
  const cancelSwapMutation = useMutation({
    mutationFn: async (swapId: string) => {
      if (!auth.member?.['Nome Escala']) {
        throw new Error('Membro não autenticado');
      }

      await supabase.rpc('set_current_member', { member_name: auth.member['Nome Escala'] });

      // 1. Verificar se a solicitação existe e pertence ao membro
      const { data: request } = await supabase
        .from('solicitacoes_troca')
        .select('status, id_solicitante')
        .eq('id', swapId)
        .single();

      if (!request) {
        throw new Error('Solicitação não encontrada');
      }

      // 2. Verificar permissão (admin ou solicitante)
      // Função auxiliar para verificar se usuário é admin
      async function checkAdminPermissions() {
        try {
          // @ts-ignore - Ignorando problemas de tipagem temporariamente
          const { data: adminMembers } = await supabase
            .from('membros')
            .select('"Nome Escala"')
            .not('"Nome Escala"', 'is', null)
            .neq('funcao', 'membro');

          const adminNames = (adminMembers || [])
            .map(m => m['Nome Escala'])
            .filter(Boolean);

          return {
            isAdmin: adminNames.includes(auth.member?.['Nome Escala'] || ''),
            adminNames
          };
        } catch (error) {
          console.error('Erro ao verificar permissões:', error);
          return { isAdmin: false, adminNames: [] };
        }
      }

      const { isAdmin, adminNames } = await checkAdminPermissions();
      console.log('Administradores encontrados:', adminNames.join(', '));
      if (!isAdmin && request.id_solicitante !== auth.member['Nome Escala']) {
        throw new Error('Você não tem permissão para cancelar esta solicitação');
      }

      // 3. Verificar se o status permite cancelamento
      if (!['aguardando_candidatos', 'aguardando_aprovacao'].includes(request.status)) {
        throw new Error('Esta solicitação não pode mais ser cancelada');
      }

      // 3. Atualizar status para cancelada
      const { error } = await supabase
        .from('solicitacoes_troca')
        .update({
          status: 'cancelada',
          data_cancelamento: new Date().toISOString()
        })
        .eq('id', swapId)
        .eq('id_solicitante', auth.member['Nome Escala']);

      if (error) {
        if (error.message.includes('violates check constraint')) {
          throw new Error('O status "cancelada" ainda não está disponível. Atualize o banco de dados.');
        }
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Solicitação cancelada com sucesso!');
      // Invalidar apenas as queries necessárias de forma sequencial
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: ['swap-requests'] });
        queryClient.invalidateQueries({ queryKey: ['all-pending-swap-requests'] });
        queryClient.invalidateQueries({ queryKey: ['user-all-swap-requests'] });
      }, 100);
    },
    onError: (error: any) => {
      console.error('Error canceling swap request:', error);
      toast.error('Erro ao cancelar solicitação: ' + (error.message || 'Erro desconhecido'));
    },
  });

  // Atualizar escala após aprovação
  const approveSwapMutation = useMutation({
    mutationFn: async ({
      requestId,
      selectedCandidate
    }: {
      requestId: string,
      selectedCandidate: string
    }) => {
      // 1. Buscar detalhes da solicitação
      const { data: request } = await supabase
        .from('solicitacoes_troca')
        .select('*')
        .eq('id', requestId)
        .single();

      if (!request) throw new Error('Solicitação não encontrada');

      // 2. Atualizar escala no Supabase
      const { error: scaleError } = await supabase
        .from('escalasPassadas')
        .update({
          [request.tipo_solicitacao === 'trocar' ? 'Membro1' : 'Lider']: selectedCandidate
        })
        .eq('ID', request.id_escala_origem);

      if (scaleError) throw scaleError;

      // 3. Atualizar status da solicitação
      const { error: requestError } = await supabase
        .from('solicitacoes_troca')
        .update({
          status: 'aceita',
          candidato_selecionado: selectedCandidate,
          data_resposta: new Date().toISOString()
        })
        .eq('id', requestId);

      if (requestError) throw requestError;
    },
    onSuccess: () => {
      toast.success('Troca aprovada e escala atualizada!');
      queryClient.invalidateQueries({ queryKey: ['all-pending-swap-requests'] });
      queryClient.invalidateQueries({ queryKey: ['member-schedules-for-swap'] });
    },
    onError: (error) => {
      toast.error('Erro ao aprovar troca: ' + error.message);
    }
  });

  const refresh = () => {
    queryClient.invalidateQueries({ queryKey: ['swap-requests'] });
    queryClient.invalidateQueries({ queryKey: ['all-pending-swap-requests'] });
    queryClient.invalidateQueries({ queryKey: ['all-swap-requests-with-schedule'] });
  };

  return {
    createSwapMutation,
    cancelSwapMutation,
    approveSwapMutation,
    refresh,
    isCreating: createSwapMutation.isPending,
    isCanceling: cancelSwapMutation.isPending,
    isApproving: approveSwapMutation.isPending,
  };
};
