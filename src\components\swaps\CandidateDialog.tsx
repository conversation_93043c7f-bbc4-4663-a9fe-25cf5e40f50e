
import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

interface CandidateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  observacoes: string;
  onObservacoesChange: (value: string) => void;
  onConfirm: () => void;
  isCreating: boolean;
}

export const CandidateDialog = ({
  open,
  onOpenChange,
  observacoes,
  onObservacoesChange,
  onConfirm,
  isCreating
}: CandidateDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Confirmar Candidatura</DialogTitle>
          <DialogDescription>
            Você está se candidatando para assumir esta escala. Deseja adicionar alguma observação?
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Observações (opcional)</label>
            <Textarea
              value={observacoes}
              onChange={(e) => onObservacoesChange(e.target.value)}
              placeholder="Adicione qualquer informação relevante..."
              className="mt-1"
              rows={3}
            />
          </div>
          
          <div className="flex gap-3 justify-end">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button 
              onClick={onConfirm}
              disabled={isCreating}
            >
              {isCreating ? 'Enviando...' : 'Confirmar Candidatura'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
