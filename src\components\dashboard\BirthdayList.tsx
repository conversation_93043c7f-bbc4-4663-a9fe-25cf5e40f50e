
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Users } from 'lucide-react';

interface Birthday {
  NomeCompleto: string;
  'Nome Escala': string;
  DataNascimento: string;
  foto_url?: string;
}

interface BirthdayListProps {
  birthdays: Birthday[] | undefined;
}

export const BirthdayList = ({ birthdays }: BirthdayListProps) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  // Função para parsear datas no formato dd/mm/yyyy com validação
  const parseBirthdate = (dateString: string) => {
    const parts = dateString.split('/');
    if (parts.length !== 3) return new Date(NaN);
    
    const [day, month, year] = parts.map(Number);
    const date = new Date(year, month - 1, day);
    
    // Valida se a data é válida
    if (isNaN(date.getTime())) {
      console.error(`Data inválida: ${dateString}`);
      return new Date(NaN);
    }
    
    return date;
  };

  // Ordena os aniversariantes pelo dia do mês
  const sortedBirthdays = React.useMemo(() => {
    if (!birthdays) return [];
    
    return [...birthdays].sort((a, b) => {
      const dateA = parseBirthdate(a.DataNascimento);
      const dateB = parseBirthdate(b.DataNascimento);
      return dateA.getDate() - dateB.getDate();
    });
  }, [birthdays]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Aniversariantes do Mês</CardTitle>
        <CardDescription>Membros da equipe aniversariando</CardDescription>
      </CardHeader>
      <CardContent>
        {sortedBirthdays.length === 0 ? (
          <div className="text-center py-4">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Nenhum aniversariante este mês</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedBirthdays.slice(0, 5).map((member) => {
              const birthDate = parseBirthdate(member.DataNascimento);
              const day = birthDate.getDate();
              
              return (
                <div key={member['Nome Escala']} className="flex items-center gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={member.foto_url} alt={member['Nome Escala']} />
                    <AvatarFallback>{getInitials(member['Nome Escala'])}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm font-medium">{member['Nome Escala']}</p>
                    <p className="text-xs text-gray-500">
                      {day.toString().padStart(2, '0')}/{birthDate.getMonth() + 1}
                    </p>
                  </div>
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">
                      {day}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
