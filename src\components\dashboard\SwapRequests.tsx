
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Check, X, Users } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { useCandidaturas } from '@/hooks/useCandidaturas';

interface SwapRequest {
  id: string;
  tipo_solicitacao: string;
  status: string;
  created_at: string;
  id_solicitante?: string;
  membro_destino?: string | null;
}

interface RequestCardProps {
  request: SwapRequest;
  adminView: boolean;
  auth: any;
  processing: string | null;
  handleRequestResponse: (requestId: string, action: 'aceita' | 'rejeitada') => void;
}

interface SwapRequestsProps {
  requests: SwapRequest[] | undefined;
  adminView?: boolean;
  onRequestUpdate?: () => void;
}

const RequestCard = ({ request, adminView, auth, processing, handleRequestResponse }: RequestCardProps) => {
  const { getCandidaturasPorSolicitacao } = useCandidaturas();
  const { data: candidaturas } = getCandidaturasPorSolicitacao(request.id);

  const getStatusLabel = () => {
    if (adminView) {
      switch(request.status) {
        case 'pendente':
          return 'Aguardando aprovação';
        case 'aceita':
          return 'Aprovada';
        case 'rejeitada':
          return 'Rejeitada';
        case 'aguardando_candidatos':
          return 'Aguardando voluntários';
        default:
          return request.status;
      }
    } else {
      switch(request.status) {
        case 'pendente':
          return 'Aguardando aprovação';
        case 'aceita':
          return 'Aprovada pela liderança';
        case 'rejeitada':
          return 'Rejeitada pela liderança';
        case 'aguardando_candidatos':
          return candidaturas?.length
            ? `Aguardando aprovação (${candidaturas.length} candidatos)`
            : 'Aguardando voluntários';
        default:
          return request.status;
      }
    }
  };

  return (
    <div key={request.id} className="p-3 border rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <div>
          <p className="font-medium">{request.tipo_solicitacao}</p>
          {adminView && (
            <p className="text-xs text-gray-500">
              {request.id_solicitante} → {request.membro_destino || 'Qualquer membro'}
            </p>
          )}
        </div>
        <div className="flex gap-2">
          <Badge variant={
            request.status === 'aceita' ? 'default' :
            request.status === 'rejeitada' ? 'destructive' : 'secondary'
          }>
            {getStatusLabel()}
          </Badge>
          {!adminView && request.status === 'aguardando_candidatos' && candidaturas?.length > 0 && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Users className="w-3 h-3" />
              {candidaturas.length}
            </Badge>
          )}
        </div>
      </div>
      
      <p className="text-sm text-gray-600 mb-2">
        {new Date(request.created_at).toLocaleDateString('pt-BR')}
      </p>

      {request.status === 'pendente' && (adminView || request.membro_destino === auth.member?.['Nome Escala']) && (
        <div className="flex gap-2 mt-2">
          <Button
            size="sm"
            onClick={() => handleRequestResponse(request.id, 'aceita')}
            disabled={processing === request.id}
            className="flex items-center gap-1"
          >
            <Check className="w-3 h-3" />
            Aceitar
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleRequestResponse(request.id, 'rejeitada')}
            disabled={processing === request.id}
            className="flex items-center gap-1"
          >
            <X className="w-3 h-3" />
            Rejeitar
          </Button>
        </div>
      )}
    </div>
  );
};

export const SwapRequests = ({ requests, adminView = false, onRequestUpdate }: SwapRequestsProps) => {
  const { auth } = useMemberAuth();
  const [processing, setProcessing] = useState<string | null>(null);

  const handleRequestResponse = async (requestId: string, action: 'aceita' | 'rejeitada') => {
    setProcessing(requestId);
    try {
      const updateData: any = {
        status: action,
        data_resposta: new Date().toISOString()
      };
      
      if (adminView) {
        updateData.responsavel_aprovacao = auth.member?.['Nome Escala'];
      }
      
      const { error } = await supabase
        .from('solicitacoes_troca')
        .update(updateData)
        .eq('id', requestId);

      if (error) throw error;
      
      toast.success(`Solicitação ${action === 'aceita' ? 'aprovada' : 'rejeitada'} com sucesso!`);
      onRequestUpdate?.();
    } catch (error) {
      console.error('Error processing request:', error);
      toast.error('Erro ao processar solicitação');
    } finally {
      setProcessing(null);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{adminView ? 'Todas Solicitações' : 'Suas Solicitações'}</CardTitle>
        <CardDescription>
          {adminView ? 'Gerencie todas as solicitações' : 'Suas solicitações recentes'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {requests && requests.length > 0 ? (
          <div className="space-y-3">
            {requests.slice(0, adminView ? 10 : 3).map((request) => (
              <RequestCard
                key={request.id}
                request={request}
                adminView={adminView}
                auth={auth}
                processing={processing}
                handleRequestResponse={handleRequestResponse}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-4">
            <RefreshCw className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Nenhuma solicitação de troca</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
