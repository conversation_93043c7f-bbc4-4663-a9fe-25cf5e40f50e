
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { Candidatura } from '@/types/candidaturaTypes';

export const useCandidaturas = () => {
  const { auth } = useMemberAuth();
  const queryClient = useQueryClient();

  // Buscar candidaturas para uma solicitação específica
  const getCandidaturasPorSolicitacao = (idSolicitacao: string) => {
    return useQuery({
      queryKey: ['candidaturas', idSolicitacao],
      queryFn: async (): Promise<Candidatura[]> => {
        const { data, error } = await supabase
          .from('candidaturas_troca')
          .select('*')
          .eq('id_solicitacao', idSolicitacao)
          .order('data_candidatura', { ascending: false });

        if (error) {
          console.error('Error fetching candidaturas:', error);
          return [];
        }
        
        return (data || []).map(c => ({
          ...c,
          status: validateCandidaturaStatus(c.status)
        }));
      },
      enabled: !!idSolicitacao,
    });
  };

  // Buscar candidaturas do membro autenticado
  const { data: minhasCandidaturasData } = useQuery({
    queryKey: ['minhas-candidaturas', auth.member?.['Nome Escala']],
    queryFn: async (): Promise<Candidatura[]> => {
      if (!auth.member?.['Nome Escala']) return [];

      await supabase.rpc('set_current_member', { member_name: auth.member['Nome Escala'] });

      const { data, error } = await supabase
        .from('candidaturas_troca')
        .select('id, id_solicitacao, membro_candidato, data_candidatura, status, observacoes') // Selecionar apenas campos necessários
        .eq('membro_candidato', auth.member['Nome Escala'])
        .order('data_candidatura', { ascending: false })
        .limit(50); // Limitar resultados

      if (error) {
        console.error('Error fetching minhas candidaturas:', error);
        return [];
      }

      return (data || []).map(c => ({
        ...c,
        status: validateCandidaturaStatus(c.status)
      }));
    },
    enabled: !!auth.member,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 15 * 60 * 1000, // 15 minutos
  });

  // Mutação para criar candidatura
  const createCandidaturaMutation = useMutation({
    mutationFn: async ({ 
      idSolicitacao, 
      observacoes 
    }: { 
      idSolicitacao: string; 
      observacoes?: string;
    }) => {
      if (!auth.member?.['Nome Escala']) {
        throw new Error('Membro não autenticado');
      }

      await supabase.rpc('set_current_member', { member_name: auth.member['Nome Escala'] });

      const { data, error } = await supabase
        .from('candidaturas_troca')
        .insert({
          id_solicitacao: idSolicitacao,
          membro_candidato: auth.member['Nome Escala'],
          observacoes: observacoes || null,
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      toast.success('Candidatura enviada com sucesso!');
      queryClient.invalidateQueries({ queryKey: ['candidaturas'] });
      queryClient.invalidateQueries({ queryKey: ['minhas-candidaturas'] });
      queryClient.invalidateQueries({ queryKey: ['swap-requests'] });
      queryClient.invalidateQueries({ queryKey: ['all-pending-swap-requests'] });
    },
    onError: (error: any) => {
      console.error('Error creating candidatura:', error);
      if (error.code === '23505') {
        toast.error('Você já se candidatou para esta solicitação');
      } else {
        toast.error('Erro ao enviar candidatura: ' + (error.message || 'Erro desconhecido'));
      }
    },
  });

  // Mutação para atualizar status da candidatura (admin)
  const updateCandidaturaStatusMutation = useMutation({
    mutationFn: async ({ 
      candidaturaId, 
      status, 
      observacoes 
    }: { 
      candidaturaId: string; 
      status: 'ativa' | 'selecionada' | 'rejeitada';
      observacoes?: string;
    }) => {
      const { data, error } = await supabase
        .from('candidaturas_troca')
        .update({
          status,
          observacoes: observacoes || null,
        })
        .eq('id', candidaturaId)
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      toast.success(`Candidatura ${data.status === 'selecionada' ? 'selecionada' : 'atualizada'} com sucesso!`);
      queryClient.invalidateQueries({ queryKey: ['candidaturas'] });
      queryClient.invalidateQueries({ queryKey: ['swap-requests'] });
    },
    onError: (error: any) => {
      console.error('Error updating candidatura:', error);
      toast.error('Erro ao atualizar candidatura: ' + (error.message || 'Erro desconhecido'));
    },
  });

  return {
    getCandidaturasPorSolicitacao,
    minhasCandidaturas: minhasCandidaturasData || [],
    createCandidaturaMutation,
    updateCandidaturaStatusMutation,
    isCreatingCandidatura: createCandidaturaMutation.isPending,
    isUpdatingCandidatura: updateCandidaturaStatusMutation.isPending,
  };
};

// Valida e converte o status da candidatura para o tipo esperado
function validateCandidaturaStatus(status: string): 'ativa' | 'selecionada' | 'rejeitada' {
  if (status === 'ativa' || status === 'selecionada' || status === 'rejeitada') {
    return status;
  }
  return 'ativa'; // Valor padrão se inválido
}
