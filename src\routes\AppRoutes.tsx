
import React from 'react';
import { Routes, Route } from 'react-router-dom';
import { AuthProvider } from '@/contexts/AuthContext';
import { MemberAuthProvider } from '@/contexts/MemberAuthContext';
import { ScheduleProvider } from '@/contexts/ScheduleContext';

// Import Pages
import Index from '@/pages/Index';
import Login from '@/pages/Login';
import Calendar from '@/pages/Calendar';
import Members from '@/pages/Members';
import AdminSwapRequests from '@/pages/AdminSwapRequests';
import NotFound from '@/pages/NotFound';
import Settings from '@/pages/Settings';
import PastSchedules from '@/pages/PastSchedules';
import ScheduleView from '@/pages/ScheduleView';
import PublicSchedule from '@/pages/PublicSchedule';

// Member Portal Pages
import MemberLogin from '@/pages/MemberLogin';
import MemberDashboard from '@/pages/MemberDashboard';
import MemberSchedules from '@/pages/MemberSchedules';
import MemberAvailability from '@/pages/MemberAvailability';
import MemberSwaps from '@/pages/MemberSwaps';
import MemberProfile from '@/pages/MemberProfile';

// Admin Layout
const AdminLayout = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>
    <ScheduleProvider>{children}</ScheduleProvider>
  </AuthProvider>
);

// Member Layout
const MemberLayoutWrapper = ({ children }: { children: React.ReactNode }) => (
  <MemberAuthProvider>{children}</MemberAuthProvider>
);

export const AppRoutes = () => (
  <Routes>
    {/* Public Routes */}
    <Route path="/" element={<Index />} />
    <Route path="/login" element={<Login />} />
    <Route path="/public/:scheduleId" element={<PublicSchedule />} />
    <Route path="/membro-login" element={<MemberLogin />} />

    {/* Admin Routes */}
    <Route
      path="/calendar"
      element={<AdminLayout><Calendar /></AdminLayout>}
    />
    <Route
      path="/members"
      element={<AdminLayout><Members /></AdminLayout>}
    />
    <Route
      path="/membros"
      element={<AdminLayout><Members /></AdminLayout>}
    />
    <Route
      path="/settings"
      element={<AdminLayout><Settings /></AdminLayout>}
    />
    <Route
      path="/past-schedules"
      element={<AdminLayout><PastSchedules /></AdminLayout>}
    />
    <Route
      path="/schedule/:id"
      element={<AdminLayout><ScheduleView /></AdminLayout>}
    />
    <Route
      path="/schedule-view"
      element={<AdminLayout><ScheduleView /></AdminLayout>}
    />
    <Route
      path="/admin-swap-requests"
      element={<AdminLayout><AdminSwapRequests /></AdminLayout>}
    />
    <Route
      path="/trocas"
      element={<AdminLayout><AdminSwapRequests /></AdminLayout>}
    />

    {/* Member Portal Routes */}
    <Route
      path="/membro-dashboard"
      element={<MemberLayoutWrapper><MemberDashboard /></MemberLayoutWrapper>}
    />
    <Route
      path="/membro-escalas"
      element={<MemberLayoutWrapper><MemberSchedules /></MemberLayoutWrapper>}
    />
    <Route
      path="/membro-disponibilidade"
      element={<MemberLayoutWrapper><MemberAvailability /></MemberLayoutWrapper>}
    />
    <Route
      path="/membro-trocas"
      element={<MemberLayoutWrapper><MemberSwaps /></MemberLayoutWrapper>}
    />
    <Route
      path="/membro-troca"
      element={<MemberLayoutWrapper><MemberSwaps /></MemberLayoutWrapper>}
    />
    <Route
      path="/membro-perfil"
      element={<MemberLayoutWrapper><MemberProfile /></MemberLayoutWrapper>}
    />

    {/* Catch all */}
    <Route path="*" element={<NotFound />} />
  </Routes>
);
