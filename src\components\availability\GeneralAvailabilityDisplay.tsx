
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Edit2, Save, X } from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { useQueryClient } from '@tanstack/react-query';

interface Availability {
  'Segunda-Feira': string;
  'Terça-Feira': string;
  'Quarta-Feira': string;
  'Quinta-Feira': string;
  'Sexta-Feira': string;
  Sabado: string;
  Domingo: string;
}

interface GeneralAvailabilityDisplayProps {
  availability: Availability | null;
}

export const GeneralAvailabilityDisplay = ({ availability }: GeneralAvailabilityDisplayProps) => {
  const { auth } = useMemberAuth();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [editedAvailability, setEditedAvailability] = useState<Availability | null>(availability);
  const [isSaving, setIsSaving] = useState(false);

  if (!availability) return null;

  const handleEdit = () => {
    setEditedAvailability(availability);
    setIsEditing(true);
  };

  const handleCancel = () => {
    setEditedAvailability(availability);
    setIsEditing(false);
  };

  const handleSave = async () => {
    if (!editedAvailability || !auth.member?.['Nome Escala']) {
      toast.error('Erro ao salvar disponibilidade');
      return;
    }

    setIsSaving(true);
    try {
      await supabase.rpc('set_current_member', { member_name: auth.member['Nome Escala'] });

      const { error } = await supabase
        .from('api_disponibilidade')
        .update({
          'Segunda-Feira': editedAvailability['Segunda-Feira'],
          'Terça-Feira': editedAvailability['Terça-Feira'],
          'Quarta-Feira': editedAvailability['Quarta-Feira'],
          'Quinta-Feira': editedAvailability['Quinta-Feira'],
          'Sexta-Feira': editedAvailability['Sexta-Feira'],
          'Sabado': editedAvailability['Sabado'],
          'Domingo': editedAvailability['Domingo']
        })
        .eq('Nome', auth.member['Nome Escala']);

      if (error) {
        console.error('Error updating availability:', error);
        toast.error('Erro ao salvar disponibilidade: ' + error.message);
        return;
      }

      toast.success('Disponibilidade atualizada com sucesso!');
      setIsEditing(false);
      
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: ['member-availability'] });
      queryClient.invalidateQueries({ queryKey: ['member-general-availability'] });
      
    } catch (error) {
      console.error('Error saving availability:', error);
      toast.error('Erro ao salvar disponibilidade');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDayChange = (day: keyof Availability, value: string) => {
    if (editedAvailability) {
      setEditedAvailability({
        ...editedAvailability,
        [day]: value
      });
    }
  };

  const getShiftOptions = (day: string) => {
    if (day === 'Domingo') {
      return [
        { value: 'NÃO', label: 'NÃO' },
        { value: 'EBD', label: 'EBD' },
        { value: '1º Culto', label: '1º Culto' },
        { value: '2º Culto', label: '2º Culto' },
        { value: 'EBD,1º Culto', label: 'EBD + 1º Culto' },
        { value: 'EBD,2º Culto', label: 'EBD + 2º Culto' },
        { value: '1º Culto,2º Culto', label: '1º + 2º Culto' },
        { value: 'EBD,1º Culto,2º Culto', label: 'Todos' }
      ];
    } else {
      return [
        { value: 'NÃO', label: 'NÃO' },
        { value: 'Manhã', label: 'Manhã' },
        { value: 'Tarde', label: 'Tarde' },
        { value: 'Noite', label: 'Noite' },
        { value: 'Manhã,Tarde', label: 'Manhã + Tarde' },
        { value: 'Manhã,Noite', label: 'Manhã + Noite' },
        { value: 'Tarde,Noite', label: 'Tarde + Noite' },
        { value: 'Manhã,Tarde,Noite', label: 'Todos' }
      ];
    }
  };

  const formatAvailabilityText = (value: string | undefined, day: string) => {
    if (!value || value === 'NÃO') return 'NÃO';
    
    if (day === 'Domingo') {
      if (value === 'EBD,1º Culto,2º Culto') return 'Todos';
      return value.replace(/,/g, ' + ');
    } else {
      if (value === 'Manhã,Tarde,Noite') return 'Todos';
      return value.replace(/,/g, ' + ');
    }
  };

  const daysOfWeek: Array<{ key: keyof Availability; label: string }> = [
    { key: 'Segunda-Feira', label: 'Segunda' },
    { key: 'Terça-Feira', label: 'Terça' },
    { key: 'Quarta-Feira', label: 'Quarta' },
    { key: 'Quinta-Feira', label: 'Quinta' },
    { key: 'Sexta-Feira', label: 'Sexta' },
    { key: 'Sabado', label: 'Sábado' },
    { key: 'Domingo', label: 'Domingo' }
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Disponibilidade Geral por Dia da Semana</CardTitle>
            <CardDescription>
              Sua disponibilidade padrão para cada dia da semana e turno
            </CardDescription>
          </div>
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Edit2 className="w-4 h-4 mr-2" />
              Editar
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleCancel} disabled={isSaving}>
                <X className="w-4 h-4 mr-2" />
                Cancelar
              </Button>
              <Button size="sm" onClick={handleSave} disabled={isSaving}>
                <Save className="w-4 h-4 mr-2" />
                {isSaving ? 'Salvando...' : 'Salvar'}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-7 gap-4">
          {daysOfWeek.map(({ key, label }) => (
            <div key={key} className="p-4 border rounded-lg space-y-2">
              <p className="font-medium text-center">{label}</p>
              {isEditing ? (
                <Select 
                  value={editedAvailability?.[key] || 'NÃO'} 
                  onValueChange={(value) => handleDayChange(key, value)}
                  disabled={isSaving}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {getShiftOptions(label).map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : (
                <div className="text-center">
                  <Badge 
                    variant={availability[key] === 'NÃO' ? 'secondary' : 'default'}
                    className="w-full justify-center text-xs"
                  >
                    {formatAvailabilityText(availability[key], label)}
                  </Badge>
                </div>
              )}
            </div>
          ))}
        </div>
        {!isEditing && (
          <p className="text-sm text-gray-600 mt-4">
            Configure seus turnos disponíveis para cada dia da semana. Clique em "Editar" para alterar.
          </p>
        )}
      </CardContent>
    </Card>
  );
};
