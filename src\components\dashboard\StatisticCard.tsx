
import React from 'react';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';

interface StatisticCardProps {
  title: string;
  value: number | string;
  subtitle: string;
}

const StatisticCard = ({ title, value, subtitle }: StatisticCardProps) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-3xl font-bold">{value}</div>
        <p className="text-muted-foreground text-sm mt-1">
          {subtitle}
        </p>
      </CardContent>
    </Card>
  );
};

export default StatisticCard;
