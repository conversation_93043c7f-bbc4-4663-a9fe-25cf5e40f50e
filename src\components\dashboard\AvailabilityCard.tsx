
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Availability {
  'Segunda-Feira': string;
  'Terça-Feira': string;
  'Quarta-Feira': string;
  'Quinta-Feira': string;
  'Sexta-Feira': string;
  Sabado: string;
  Domingo: string;
}

interface AvailabilityCardProps {
  availability: Availability | null;
}

export const AvailabilityCard = ({ availability }: AvailabilityCardProps) => {
  if (!availability) return null;

  const days = ['Segunda-Feira', 'Terça-Feira', 'Quarta-Feira', 'Quinta-Feira', 'Sexta-Feira', 'Sabado', 'Domingo'];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Minha Disponibilidade</CardTitle>
        <CardDescription>Sua disponibilidade atual para servir</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
          {days.map((day) => (
            <div key={day} className="text-center p-2 border rounded">
              <p className="text-xs font-medium mb-1">{day.slice(0, 3)}</p>
              <Badge variant={availability[day as keyof Availability] === 'SIM' ? 'default' : 'secondary'} className="text-xs">
                {availability[day as keyof Availability] || 'N/A'}
              </Badge>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};
