
import React, { useState } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Calendar, Clock, User, Users, Check, X, MapPin, AlertTriangle, ChevronDown, ChevronUp } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';
import { useCandidaturas } from '@/hooks/useCandidaturas';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useAdminData } from '@/hooks/useAdminData';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface CollapsibleAdminSwapCardProps {
  request: SwapRequest & { scheduleData?: any };
  onRequestUpdate?: () => void;
  isOpen: boolean;
  onToggle: () => void;
}

const formatDateBR = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'dd/MM/yyyy', { locale: ptBR });
  } catch {
    return dateString;
  }
};

const formatDateTimeBR = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'dd/MM/yyyy HH:mm', { locale: ptBR });
  } catch {
    return dateString;
  }
};

export const CollapsibleAdminSwapCard = ({ request, onRequestUpdate, isOpen, onToggle }: CollapsibleAdminSwapCardProps) => {
  const { getCandidaturasPorSolicitacao } = useCandidaturas();
  const { data: candidaturas } = getCandidaturasPorSolicitacao(request.id);
  const [processing, setProcessing] = useState(false);
  const [selectedCandidato, setSelectedCandidato] = useState<string>('');
  const { adminScaleName } = useAdminData();

  const hasCandidates = candidaturas && candidaturas.length > 0;
  const showActions = request.status === 'aguardando_aprovacao' && hasCandidates;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'aguardando_candidatos':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Aguardando Voluntários</Badge>;
      case 'aguardando_aprovacao':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Pronta para Aprovação</Badge>;
      case 'aceita':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Aprovada</Badge>;
      case 'rejeitada':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejeitada</Badge>;
      case 'cancelada':
        return <Badge variant="secondary">Cancelada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleApproveSwap = async () => {
    if (!selectedCandidato) {
      toast.error('Selecione um candidato para aprovar');
      return;
    }

    setProcessing(true);
    try {
      console.log('Starting approval process for request:', request.id);
      console.log('Selected candidate:', selectedCandidato);
      console.log('Schedule data:', request.scheduleData);
      console.log('Admin scale name:', adminScaleName);

      // 1. Atualizar escala na tabela escalasPassadas (apenas se temos dados da escala)
      if (request.scheduleData) {
        const updateField = getUpdateFieldForMember(request.scheduleData, request.id_solicitante);
        console.log('Update field found:', updateField);
        
        if (updateField) {
          const { error: updateError } = await supabase
            .from('escalasPassadas')
            .update({ [updateField]: selectedCandidato })
            .eq('ID', request.id_escala_origem);

          if (updateError) {
            console.error('Error updating schedule:', updateError);
            throw new Error(`Erro ao atualizar escala: ${updateError.message}`);
          }
          console.log('Schedule updated successfully');
        } else {
          console.warn('No update field found for member:', request.id_solicitante);
        }
      }

      // 2. Atualizar status da solicitação
      console.log('Updating request status...');
      const { error: statusError } = await supabase
        .from('solicitacoes_troca')
        .update({
          status: 'aceita',
          candidato_selecionado: selectedCandidato,
          data_resposta: new Date().toISOString(),
          responsavel_aprovacao: adminScaleName || 'Admin'
        })
        .eq('id', request.id);

      if (statusError) {
        console.error('Error updating status:', statusError);
        throw new Error(`Erro ao atualizar status: ${statusError.message}`);
      }

      console.log('Request status updated successfully with admin name:', adminScaleName);
      toast.success('Troca aprovada com sucesso! Escala atualizada e histórico registrado automaticamente.');
      onRequestUpdate?.();

      // Refresh da página após aprovação para garantir dados atualizados
      setTimeout(() => {
        window.location.reload();
      }, 1500); // Aguarda 1.5s para mostrar o toast antes do refresh
    } catch (error: any) {
      console.error('Error in approval process:', error);
      toast.error(error.message || 'Erro ao aprovar troca');
    } finally {
      setProcessing(false);
    }
  };

  const handleRejectSwap = async () => {
    setProcessing(true);
    try {
      console.log('Rejecting swap request:', request.id);
      console.log('Admin scale name:', adminScaleName);
      
      const { error } = await supabase
        .from('solicitacoes_troca')
        .update({
          status: 'rejeitada',
          data_resposta: new Date().toISOString(),
          responsavel_aprovacao: adminScaleName || 'Admin'
        })
        .eq('id', request.id);

      if (error) {
        console.error('Error rejecting request:', error);
        throw new Error(`Erro ao rejeitar solicitação: ${error.message}`);
      }

      console.log('Request rejected successfully with admin name:', adminScaleName);
      toast.success('Solicitação rejeitada e registrada no histórico automaticamente');
      onRequestUpdate?.();
    } catch (error: any) {
      console.error('Error rejecting swap:', error);
      toast.error(error.message || 'Erro ao rejeitar solicitação');
    } finally {
      setProcessing(false);
    }
  };

  const getUpdateFieldForMember = (scheduleData: any, memberName: string) => {
    if (scheduleData.Lider === memberName) return 'Lider';
    if (scheduleData.Membro1 === memberName) return 'Membro1';
    if (scheduleData.Membro2 === memberName) return 'Membro2';
    if (scheduleData.Membro3 === memberName) return 'Membro3';
    if (scheduleData.Membro4 === memberName) return 'Membro4';
    return null;
  };

  const handleCandidateSelect = (candidateName: string) => {
    setSelectedCandidato(candidateName === selectedCandidato ? '' : candidateName);
  };

  return (
    <Card className="w-full border-border hover:border-primary/20 transition-all duration-300">
      <Collapsible open={isOpen} onOpenChange={onToggle}>
        <CollapsibleTrigger asChild>
          <CardHeader className="bg-secondary/50 cursor-pointer hover:bg-secondary/70 transition-colors py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <User className="w-4 h-4 text-primary" />
                <div>
                  <h3 className="font-medium text-foreground">{request.id_solicitante}</h3>
                  {request.scheduleData && (
                    <p className="text-sm text-muted-foreground">
                      {formatDateBR(request.scheduleData.Data)} - {request.scheduleData.Turno}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusBadge(request.status)}
                {hasCandidates && (
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    {candidaturas.length} candidato(s)
                  </Badge>
                )}
                {isOpen ? (
                  <ChevronUp className="w-4 h-4 text-muted-foreground" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-muted-foreground" />
                )}
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="space-y-4 p-4">
            {/* Informações da escala */}
            {request.scheduleData ? (
              <div className="p-3 bg-blue-50 dark:bg-primary/10 rounded-lg border border-primary/20 space-y-2">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-primary" />
                  <span className="font-medium text-foreground">Dia do Serviço:</span>
                  <span className="text-muted-foreground">{request.scheduleData.Dia}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-primary" />
                  <span className="font-medium text-foreground">Data:</span>
                  <span className="text-muted-foreground">{formatDateBR(request.scheduleData.Data)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4 text-primary" />
                  <span className="font-medium text-foreground">Turno:</span>
                  <span className="text-muted-foreground">{request.scheduleData.Turno}</span>
                </div>
              </div>
            ) : (
              <div className="p-3 bg-amber-50 dark:bg-warning/10 border border-warning/50 rounded-lg">
                <div className="flex items-center gap-2 text-warning-foreground dark:text-warning">
                  <AlertTriangle className="w-4 h-4" />
                  <span className="font-medium">Escala ID: {request.id_escala_origem}</span>
                </div>
                <p className="text-sm text-amber-900 dark:text-warning/80 mt-1">
                  Dados da escala não encontrados.
                </p>
              </div>
            )}

            {/* Motivo */}
            {request.mensagem && (
              <div className="p-3 bg-accent/10 border border-accent/20 rounded-lg">
                <p className="text-sm font-medium text-accent-foreground dark:text-accent mb-1">Motivo:</p>
                <p className="text-accent/90 dark:text-accent/80">{request.mensagem}</p>
              </div>
            )}

            {/* Lista de candidatos */}
            {hasCandidates && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4 text-success" />
                  <span className="font-medium text-foreground">Candidatos ({candidaturas.length}):</span>
                </div>
                <div className="space-y-2">
                  {candidaturas.map((candidatura) => (
                    <div 
                      key={candidatura.id} 
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedCandidato === candidatura.membro_candidato
                          ? 'border-primary bg-primary/5'
                          : 'border-border bg-background hover:bg-secondary/50'
                      }`}
                      onClick={() => showActions && handleCandidateSelect(candidatura.membro_candidato)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-foreground">{candidatura.membro_candidato}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatDateTimeBR(candidatura.data_candidatura)}
                          </p>
                          {candidatura.observacoes && (
                            <p className="text-sm mt-1 text-muted-foreground">{candidatura.observacoes}</p>
                          )}
                        </div>
                        {selectedCandidato === candidatura.membro_candidato && showActions && (
                          <Check className="w-4 h-4 text-primary" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Ações admin */}
            {showActions && (
              <div className="flex flex-col gap-3 sm:flex-row pt-4 border-t border-border">
                <Button
                  onClick={handleApproveSwap}
                  disabled={processing || !selectedCandidato}
                  className="flex items-center gap-2 w-full sm:flex-1 bg-green-600 hover:bg-green-700 text-white"
                >
                  <Check className="w-4 h-4" />
                  {processing ? 'Processando...' : 'Aprovar Troca'}
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleRejectSwap}
                  disabled={processing}
                  className="flex items-center gap-2 w-full sm:flex-1"
                >
                  <X className="w-4 h-4" />
                  Rejeitar
                </Button>
              </div>
            )}

            {/* Ações básicas para solicitações sem candidatos ou já processadas */}
            {!showActions && request.status !== 'aceita' && request.status !== 'rejeitada' && request.status !== 'cancelada' && (
              <div className="flex gap-3 pt-4 border-t border-border">
                <Button
                  variant="destructive"
                  onClick={handleRejectSwap}
                  disabled={processing}
                  className="flex items-center gap-2"
                >
                  <X className="w-4 h-4" />
                  {processing ? 'Processando...' : 'Cancelar Solicitação'}
                </Button>
              </div>
            )}

            {/* Status quando não há candidatos */}
            {!hasCandidates && request.status === 'aguardando_candidatos' && (
              <div className="text-center py-4 text-muted-foreground bg-secondary/50 rounded-lg">
                <Users className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>Aguardando voluntários para esta troca</p>
              </div>
            )}

            {/* Informações de resposta para solicitações finalizadas */}
            {(request.status === 'aceita' || request.status === 'rejeitada') && request.data_resposta && (
              <div className="p-3 bg-secondary/50 border border-border rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-foreground">
                      {request.status === 'aceita' ? 'Aprovada' : 'Rejeitada'} em:{' '}
                      {formatDateTimeBR(request.data_resposta)}
                    </p>
                    {request.candidato_selecionado && (
                      <p className="text-sm text-muted-foreground">
                        Candidato selecionado: {request.candidato_selecionado}
                      </p>
                    )}
                    {request.responsavel_aprovacao && (
                      <p className="text-sm text-muted-foreground">
                        Responsável: {request.responsavel_aprovacao}
                      </p>
                    )}
                    <p className="text-sm text-green-600 font-medium flex items-center gap-1 mt-1">
                      <Check className="w-3 h-3" />
                      Registrado no histórico de trocas automaticamente
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};
