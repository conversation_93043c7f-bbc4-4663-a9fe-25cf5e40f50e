
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, RefreshCw, Users, Clock, CheckCircle } from 'lucide-react';

interface DashboardStatsProps {
  upcomingSchedulesCount: number;
  swapRequestsCount: number;
  swapsWithCandidatesCount?: number;
  swapsAwaitingCandidatesCount?: number;
  birthdaysCount: number;
  memberStatus: string;
  adminView?: boolean;
}

export const DashboardStats = ({ 
  upcomingSchedulesCount, 
  swapRequestsCount, 
  swapsWithCandidatesCount,
  swapsAwaitingCandidatesCount,
  birthdaysCount, 
  memberStatus,
  adminView = false
}: DashboardStatsProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Próximas Escalas
          </CardTitle>
          <Calendar className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{upcomingSchedulesCount}</div>
        </CardContent>
      </Card>

      {adminView ? (
        <>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pendentes de Aprovação
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{swapsWithCandidatesCount || 0}</div>
              <p className="text-xs text-muted-foreground">
                Com candidatos
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Aguardando Voluntários
              </CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{swapsAwaitingCandidatesCount || 0}</div>
              <p className="text-xs text-muted-foreground">
                Sem candidatos
              </p>
            </CardContent>
          </Card>
        </>
      ) : (
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Solicitações de Troca
            </CardTitle>
            <RefreshCw className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{swapRequestsCount}</div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            Aniversariantes
          </CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{birthdaysCount}</div>
        </CardContent>
      </Card>

    </div>
  );
};
