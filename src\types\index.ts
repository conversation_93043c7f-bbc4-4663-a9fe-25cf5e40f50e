export interface Person {
  _lineNumber: number;
  ID: string;
  Nome: string;
  "Segunda-Feira": string;
  "Terça-Feira": string;
  "Quarta-Feira": string;
  "Quinta-Feira": string;
  "Sexta-Feira": string;
  Sabado: string;
  Domingo: string;
  Status?: string; // Added Status property
}

export type Shift = "Manhã" | "Tarde" | "Noite" | "EBD" | "1º Culto" | "2º Culto";
export type ShiftType = "morning" | "afternoon" | "evening";

export type DayOfWeek =
  | "Segunda-Feira"
  | "Terça-Feira"
  | "Quarta-Feira"
  | "Quinta-Feira"
  | "Sexta-Feira"
  | "Sabado"
  | "Domingo";

export interface SelectedPerson {
  id: string;
  name: string;
}

export interface DaySchedule {
  date: Date;
  shifts: {
    morning: string[];
    afternoon: string[];
    evening: string[];
  };
}

export interface ScheduleState {
  year: number;
  month: number;
  maxAssignmentsPerPerson: number;
  selectedDays: Date[];
  schedule: Record<string, DaySchedule>;
}

export interface AuthState {
  isAuthenticated: boolean;
  username: string;
}

export interface ScheduleData {
  ID: string;
  Dia: string;
  Data: string;
  Turno: string;
  "Start time": string;
  "End time": string;
  Lider: string;
  Membro1: string;
  Membro2: string;
  Membro3: string;
  Membro4: string;
  Membro5?: string;
  Membro6?: string;
  Membro7?: string;
  Membro8?: string;
  Membro9?: string;
}

export interface DashboardData {
  _lineNumber: number;
  data: string;
  id: string;
  "Nome Lider": string;
  "Tipo Culto": string;
  Faltas: string;
  carros: string;
  motos: string;
  bikes: string;
  "Alteração ?": string;
  Motorista1?: string;
  tel_moto?: string;
  Relator?: string;
  Relatado?: string;
  "Tel relator"?: string;
  "Versão sentinela"?: string;
  Desatentos?: string;
  "enviado lider ?"?: string;
}
