
// Re-export all functions to maintain compatibility
export { fetchMemberSwapRequests } from './swapRequestService';
export {
  fetchAllSwapRequestsCount,
  fetchPendingSwapsCount,
  fetchSwapsWithCandidates,
  fetchSwapsAwaitingCandidates,
  fetchApprovedSwapsCount
} from './swapStatsService';
export { fetchSwapRequestsWithScheduleData } from './swapDataService';

// Keep the updateSwapRequestStatus function for compatibility
import { supabase } from '@/integrations/supabase/client';
import { SwapRequest } from '@/types/swapTypes';

export async function updateSwapRequestStatus(
  id: string,
  newStatus: string,
  adminId: string,
  observacoes?: string
): Promise<SwapRequest> {
  try {
    const updates = {
      status: newStatus,
      responsavel_aprovacao: adminId,
      data_resposta: new Date().toISOString(),
      observacoes_admin: observacoes || undefined
    };

    const { data, error } = await supabase
      .from('solicitacoes_troca')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating swap request status:', error);
      throw error;
    }

    return {
      id: data.id,
      id_solicitante: data.id_solicitante,
      data_solicitacao: data.data_solicitacao,
      mensagem: data.mensagem || '',
      status: data.status as 'pendente' | 'aguardando_candidatos' | 'aguardando_aprovacao' | 'aceita' | 'rejeitada',
      created_at: data.created_at,
      updated_at: data.updated_at,
      data_resposta: data.data_resposta || undefined,
      id_escala_origem: data.id_escala_origem,
      id_escala_destino: data.id_escala_destino || undefined,
      membro_destino: data.membro_destino || undefined,
      tipo_solicitacao: data.tipo_solicitacao || 'troca',
      candidato_selecionado: data.candidato_selecionado || undefined,
      data_selecao_candidato: data.data_selecao_candidato || undefined,
      observacoes_admin: data.observacoes_admin || undefined,
      responsavel_aprovacao: data.responsavel_aprovacao || undefined
    };
  } catch (error) {
    console.error('Failed to update swap request status:', error);
    throw error;
  }
}
