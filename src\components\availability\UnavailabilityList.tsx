
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';

interface Unavailability {
  id: string;
  data_indisponivel: string;
  motivo: string;
}

interface UnavailabilityListProps {
  unavailabilities: Unavailability[] | undefined;
  onDelete: (id: string) => void;
}

export const UnavailabilityList = ({ unavailabilities, onDelete }: UnavailabilityListProps) => {
  const formatDate = (dateString: string) => {
    const [year, month, day] = dateString.split('-').map(Number);
    const date = new Date(year, month - 1, day);
    return date.toLocaleDateString('pt-BR');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Minhas Indisponibilidades</CardTitle>
        <CardDescription>
          Suas datas de indisponibilidade futuras
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {unavailabilities && unavailabilities.length > 0 ? (
            unavailabilities.map((unavailability) => (
              <div key={unavailability.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">
                    {formatDate(unavailability.data_indisponivel)}
                  </p>
                  <p className="text-sm text-gray-600">{unavailability.motivo}</p>
                </div>
                <Button
                  onClick={() => onDelete(unavailability.id)}
                  variant="outline"
                  size="sm"
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-4">
              Nenhuma indisponibilidade cadastrada
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
