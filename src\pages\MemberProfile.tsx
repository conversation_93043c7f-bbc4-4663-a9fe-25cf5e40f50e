
import React, { useState } from 'react';
import { MemberLayout } from '@/components/MemberLayout';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Briefcase, 
  Heart,
  Edit3,
  Save,
  X,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

const MemberProfile = () => {
  const { auth, updateMember } = useMemberAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    email: '',
    Telefone: '',
    Endereço: '',
    Profiss<PERSON>: ''
  });

  const member = auth.member;

  React.useEffect(() => {
    if (member) {
      setEditForm({
        email: (member as any).email || '',
        Telefone: member.Telefone || '',
        Endereço: member.Endereço || '',
        Profissão: member.Profissão || ''
      });
    }
  }, [member]);

  const handleSave = async () => {
    if (!member) return;

    try {
      const { error } = await supabase
        .from('membros')
        .update(editForm)
        .eq('Nome Escala', member['Nome Escala']);

      if (error) throw error;

      updateMember(editForm);
      toast.success('Perfil atualizado com sucesso!');
      setIsEditing(false);
    } catch (error) {
      console.error('Erro ao atualizar perfil:', error);
      toast.error('Erro ao atualizar perfil');
    }
  };

  if (!member) {
    return (
      <MemberLayout>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="text-slate-600">Carregando perfil...</p>
          </div>
        </div>
      </MemberLayout>
    );
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'ativo':
        return 'bg-green-100 text-green-700 border-green-200';
      case 'inativo':
        return 'bg-red-100 text-red-700 border-red-200';
      default:
        return 'bg-slate-100 text-slate-700 border-slate-200';
    }
  };

  return (
    <MemberLayout>
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-slate-50">
        {/* Header Section */}
        <div className="bg-white border-b border-slate-200 shadow-sm">
          <div className="max-w-6xl mx-auto px-4 py-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-slate-900">Meu Perfil</h1>
                <p className="text-slate-600 mt-1">Gerencie suas informações pessoais</p>
              </div>
              <Button
                variant={isEditing ? "outline" : "default"}
                onClick={() => setIsEditing(!isEditing)}
                className={isEditing ? "border-slate-300 hover:bg-slate-50" : "bg-orange-500 hover:bg-orange-600 text-white shadow-md"}
              >
                {isEditing ? (
                  <>
                    <X className="w-4 h-4 mr-2" />
                    Cancelar
                  </>
                ) : (
                  <>
                    <Edit3 className="w-4 h-4 mr-2" />
                    Editar Perfil
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-6xl mx-auto px-4 py-8">
          {/* Profile Overview Card */}
          <Card className="mb-8 border-slate-200 shadow-lg bg-white">
            <CardHeader className="bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-t-lg">
              <div className="flex flex-col sm:flex-row sm:items-center gap-6">
                <Avatar className="w-20 h-20 border-4 border-white shadow-lg">
                  <AvatarImage 
                    src={member.foto_url || member['Link Foto']} 
                    alt={member['Nome Escala']} 
                  />
                  <AvatarFallback className="bg-white text-orange-600 text-xl font-semibold">
                    {getInitials(member['Nome Escala'])}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <CardTitle className="text-2xl text-white mb-2">
                    {member.NomeCompleto || member['Nome Escala']}
                  </CardTitle>
                  <p className="text-orange-100 font-medium mb-3">{member['Nome Escala']}</p>
                  <div className="flex flex-wrap gap-2">
                    <Badge className={`${getStatusColor(member.Status)} border`}>
                      {member.Status || 'Ativo'}
                    </Badge>
                    {member.CargoMinisterio && (
                      <Badge variant="outline" className="bg-white text-orange-700 border-orange-200">
                        <Shield className="w-3 h-3 mr-1" />
                        {member.CargoMinisterio}
                      </Badge>
                    )}
                    {member.Equipe && (
                      <Badge variant="outline" className="bg-white text-slate-700 border-slate-300">
                        {member.Equipe}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="p-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Contact Information */}
                <div className="space-y-6">
                  <div className="flex items-center gap-3 pb-3 border-b border-orange-100">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <User className="w-5 h-5 text-orange-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-slate-800">Informações de Contato</h3>
                  </div>
                  
                  <div className="space-y-5">
                    <div>
                      <Label className="text-slate-700 flex items-center gap-2 mb-3 font-medium">
                        <Mail className="w-4 h-4 text-orange-500" />
                        E-mail
                      </Label>
                      {isEditing ? (
                        <Input
                          type="email"
                          value={editForm.email}
                          onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                          className="border-slate-300 focus:border-orange-500 focus:ring-orange-500 bg-white"
                          placeholder="<EMAIL>"
                        />
                      ) : (
                        <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                          <p className="text-slate-700">{(member as any).email || 'Não informado'}</p>
                        </div>
                      )}
                    </div>

                    <div>
                      <Label className="text-slate-700 flex items-center gap-2 mb-3 font-medium">
                        <Phone className="w-4 h-4 text-orange-500" />
                        Telefone
                      </Label>
                      {isEditing ? (
                        <Input
                          value={editForm.Telefone}
                          onChange={(e) => setEditForm({ ...editForm, Telefone: e.target.value })}
                          className="border-slate-300 focus:border-orange-500 focus:ring-orange-500 bg-white"
                          placeholder="(11) 99999-9999"
                        />
                      ) : (
                        <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                          <p className="text-slate-700">{member.Telefone || 'Não informado'}</p>
                        </div>
                      )}
                    </div>

                    <div>
                      <Label className="text-slate-700 flex items-center gap-2 mb-3 font-medium">
                        <MapPin className="w-4 h-4 text-orange-500" />
                        Endereço
                      </Label>
                      {isEditing ? (
                        <Textarea
                          value={editForm.Endereço}
                          onChange={(e) => setEditForm({ ...editForm, Endereço: e.target.value })}
                          className="border-slate-300 focus:border-orange-500 focus:ring-orange-500 bg-white"
                          rows={3}
                          placeholder="Seu endereço completo"
                        />
                      ) : (
                        <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                          <p className="text-slate-700">{member.Endereço || 'Não informado'}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Personal Information */}
                <div className="space-y-6">
                  <div className="flex items-center gap-3 pb-3 border-b border-orange-100">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Briefcase className="w-5 h-5 text-orange-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-slate-800">Informações Pessoais</h3>
                  </div>
                  
                  <div className="space-y-5">
                    <div>
                      <Label className="text-slate-700 flex items-center gap-2 mb-3 font-medium">
                        <Calendar className="w-4 h-4 text-orange-500" />
                        Data de Nascimento
                      </Label>
                      <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                        <p className="text-slate-700">{member.DataNascimento || 'Não informado'}</p>
                      </div>
                    </div>

                    <div>
                      <Label className="text-slate-700 flex items-center gap-2 mb-3 font-medium">
                        <Heart className="w-4 h-4 text-orange-500" />
                        Estado Civil
                      </Label>
                      <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                        <p className="text-slate-700">{member.EstadoCivil || 'Não informado'}</p>
                      </div>
                    </div>

                    <div>
                      <Label className="text-slate-700 flex items-center gap-2 mb-3 font-medium">
                        <Briefcase className="w-4 h-4 text-orange-500" />
                        Profissão
                      </Label>
                      {isEditing ? (
                        <Input
                          value={editForm.Profissão}
                          onChange={(e) => setEditForm({ ...editForm, Profissão: e.target.value })}
                          className="border-slate-300 focus:border-orange-500 focus:ring-orange-500 bg-white"
                          placeholder="Sua profissão"
                        />
                      ) : (
                        <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                          <p className="text-slate-700">{member.Profissão || 'Não informado'}</p>
                        </div>
                      )}
                    </div>

                    <div>
                      <Label className="text-slate-700 mb-3 block font-medium">CPF</Label>
                      <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                        <p className="text-slate-700">{member.CPF || 'Não informado'}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Ministry Information */}
              <div className="mt-8 pt-8 border-t border-slate-200">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Shield className="w-5 h-5 text-orange-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-slate-800">Informações do Ministério</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label className="text-slate-700 mb-3 block font-medium">Equipe</Label>
                    <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                      <p className="text-slate-700">{member.Equipe || 'Não informado'}</p>
                    </div>
                  </div>
                  <div>
                    <Label className="text-slate-700 mb-3 block font-medium">Cargo no Ministério</Label>
                    <div className="p-4 bg-slate-50 rounded-lg border border-slate-200">
                      <p className="text-slate-700">{member.CargoMinisterio || 'Não informado'}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              {isEditing && (
                <div className="flex justify-end pt-8 border-t border-slate-200 mt-8">
                  <Button 
                    onClick={handleSave}
                    className="bg-orange-500 hover:bg-orange-600 text-white shadow-md px-8"
                  >
                    <Save className="w-4 h-4 mr-2" />
                    Salvar Alterações
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </MemberLayout>
  );
};

export default MemberProfile;
