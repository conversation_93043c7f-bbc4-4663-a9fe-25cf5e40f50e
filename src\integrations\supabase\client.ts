// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://sxjbyouxfrxfnuefxoki.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN4amJ5b3V4ZnJ4Zm51ZWZ4b2tpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM5MjIxNzcsImV4cCI6MjA0OTQ5ODE3N30.0wObZYK23ZVFi02vYLUztqjtZRP1yEiuKj7sTEPW950";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: window.localStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
});