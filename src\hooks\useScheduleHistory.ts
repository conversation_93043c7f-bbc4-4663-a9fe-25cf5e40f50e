
import { useState } from 'react';

export function useScheduleHistory<T>(initialState: T) {
  const [history, setHistory] = useState<T[]>([initialState]);
  const [historyPosition, setHistoryPosition] = useState(0);
  
  const saveToHistory = (data: T) => {
    // If we're not at the end of the history, truncate
    if (historyPosition < history.length - 1) {
      setHistory(history.slice(0, historyPosition + 1));
    }
    
    // Add new state to history
    setHistory([...history, data]);
    setHistoryPosition(historyPosition + 1);
  };
  
  const handleUndo = () => {
    if (historyPosition > 0) {
      // Move back in history
      setHistoryPosition(historyPosition - 1);
    }
  };
  
  const handleRedo = () => {
    if (historyPosition < history.length - 1) {
      // Move forward in history
      setHistoryPosition(historyPosition + 1);
    }
  };
  
  return {
    history,
    historyPosition,
    currentState: history[historyPosition],
    saveToHistory,
    handleUndo,
    handleRedo
  };
}
