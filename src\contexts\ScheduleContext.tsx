
import React, { createContext, useContext, useState } from 'react';
import { format } from 'date-fns';
import { DaySchedule } from '@/types';

// Define the types for the context state
interface ScheduleContextType {
  scheduleState: {
    month: number;
    year: number;
    selectedDays: Date[];
    schedule: Record<string, DaySchedule>;
    serviceLimits: number;
    breakDays: number;
  };
  setCurrentMonth: (month: number) => void;
  setCurrentYear: (year: number) => void;
  toggleDaySelection: (day: Date) => void;
  addPersonToShift: (day: Date, shift: string, personId: string) => void;
  removePersonFromShift: (day: Date, shift: string, personId: string) => void;
  setServiceLimits: (limit: number) => void;
  setBreakDays: (days: number) => void;
  countAssignments: (personId: string) => number;
  getScheduleForDay: (date: Date) => DaySchedule | undefined;
  clearSchedule: () => void;
  swapPersonsInShift: (
    date1: Date, 
    shift1: string, 
    personId1: string,
    date2: Date,
    shift2: string,
    personId2: string
  ) => void;
  updateShiftOrder: (date: Date, shiftKey: string, newOrder: string[]) => void;
  wasPersonScheduledRecently: (personId: string, currentDate: Date) => boolean;
}

// Create the context
const ScheduleContext = createContext<ScheduleContextType | undefined>(undefined);

export const ScheduleProvider = ({ children }: { children: React.ReactNode }) => {
  // Current month and year for the calendar
  const [month, setMonth] = useState<number>(new Date().getMonth());
  const [year, setYear] = useState<number>(new Date().getFullYear());
  
  // Selected days for scheduling
  const [selectedDays, setSelectedDays] = useState<Date[]>([]);
  
  // Schedule data for selected days
  const [schedule, setSchedule] = useState<Record<string, DaySchedule>>({});
  
  // Service limits per person
  const [serviceLimits, setServiceLimits] = useState<number>(2);
  
  // Break days between services
  const [breakDays, setBreakDays] = useState<number>(21);
  
  // Set the current month
  const setCurrentMonth = (newMonth: number) => {
    setMonth(newMonth);
  };
  
  // Set the current year
  const setCurrentYear = (newYear: number) => {
    setYear(newYear);
  };
  
  // Toggle selection of a day
  const toggleDaySelection = (day: Date) => {
    const dateStr = format(day, "yyyy-MM-dd");
    
    setSelectedDays(prevDays => {
      // If the day is already selected, remove it
      if (prevDays.some(d => format(d, "yyyy-MM-dd") === dateStr)) {
        return prevDays.filter(d => format(d, "yyyy-MM-dd") !== dateStr);
      }
      // Otherwise, add it
      return [...prevDays, day];
    });
  };
  
  // Get the schedule for a specific day
  const getScheduleForDay = (date: Date): DaySchedule | undefined => {
    const dateKey = format(date, "yyyy-MM-dd");
    return schedule[dateKey];
  };
  
  // Add a person to a shift on a specific day
  const addPersonToShift = (day: Date, shift: string, personId: string) => {
    const dateKey = format(day, "yyyy-MM-dd");
    const shiftKey = mapShiftLabelToKey(shift);
    
    if (!shiftKey) return;
    
    setSchedule(prevSchedule => {
      // Create a new day schedule if it doesn't exist
      const daySchedule = prevSchedule[dateKey] || {
        date: day,
        shifts: {
          morning: [],
          afternoon: [],
          evening: []
        }
      };
      
      // Add the person if not already in the shift
      if (!daySchedule.shifts[shiftKey].includes(personId)) {
        return {
          ...prevSchedule,
          [dateKey]: {
            ...daySchedule,
            shifts: {
              ...daySchedule.shifts,
              [shiftKey]: [...daySchedule.shifts[shiftKey], personId]
            }
          }
        };
      }
      
      return prevSchedule;
    });
  };
  
  // Remove a person from a shift on a specific day
  const removePersonFromShift = (day: Date, shift: string, personId: string) => {
    const dateKey = format(day, "yyyy-MM-dd");
    const shiftKey = mapShiftLabelToKey(shift);
    
    if (!shiftKey) return;
    
    setSchedule(prevSchedule => {
      // If the day doesn't exist in the schedule, do nothing
      if (!prevSchedule[dateKey]) return prevSchedule;
      
      const updatedDaySchedule = {
        ...prevSchedule[dateKey],
        shifts: {
          ...prevSchedule[dateKey].shifts,
          [shiftKey]: prevSchedule[dateKey].shifts[shiftKey].filter(id => id !== personId)
        }
      };
      
      return {
        ...prevSchedule,
        [dateKey]: updatedDaySchedule
      };
    });
  };
  
  // Count how many times a person is assigned across all days and shifts
  const countAssignments = (personId: string): number => {
    let count = 0;
    
    Object.values(schedule).forEach(daySchedule => {
      Object.values(daySchedule.shifts).forEach(shiftPersons => {
        if (shiftPersons.includes(personId)) {
          count++;
        }
      });
    });
    
    return count;
  };
  
  // Check if a person was scheduled recently based on break days
  const wasPersonScheduledRecently = (personId: string, currentDate: Date): boolean => {
    const currentTime = currentDate.getTime();
    const breakDaysMs = breakDays * 24 * 60 * 60 * 1000; // Convert break days to milliseconds
    
    // Check all schedules to see if the person was assigned within the break period
    for (const dateKey in schedule) {
      const scheduleDate = schedule[dateKey].date;
      const scheduleTime = scheduleDate.getTime();
      const timeDiff = Math.abs(currentTime - scheduleTime);
      
      // Skip if the date is the same as current date
      if (format(scheduleDate, "yyyy-MM-dd") === format(currentDate, "yyyy-MM-dd")) continue;
      
      // Check if within break period
      if (timeDiff < breakDaysMs) {
        const daySchedule = schedule[dateKey];
        // Check if person is assigned on this day
        for (const shiftKey in daySchedule.shifts) {
          if (daySchedule.shifts[shiftKey as keyof typeof daySchedule.shifts].includes(personId)) {
            return true;
          }
        }
      }
    }
    
    return false;
  };
  
  // Clear the entire schedule
  const clearSchedule = () => {
    setSelectedDays([]);
    setSchedule({});
  };
  
  // Map shift label to key
  const mapShiftLabelToKey = (label: string): "morning" | "afternoon" | "evening" | null => {
    switch (label) {
      case "Manhã":
      case "EBD":
        return "morning";
      case "Tarde":
      case "1º Culto":
        return "afternoon";
      case "Noite":
      case "2º Culto":
        return "evening";
      default:
        console.warn(`Turno não reconhecido: "${label}"`);
        return null;
    }
  };

  // Swap two persons between shifts
  const swapPersonsInShift = (
    date1: Date, 
    shift1: string, 
    personId1: string,
    date2: Date,
    shift2: string,
    personId2: string
  ) => {
    const dateKey1 = format(date1, "yyyy-MM-dd");
    const dateKey2 = format(date2, "yyyy-MM-dd");
    const shiftKey1 = mapShiftLabelToKey(shift1);
    const shiftKey2 = mapShiftLabelToKey(shift2);
    
    if (!shiftKey1 || !shiftKey2) return;
    
    setSchedule(prevSchedule => {
      // Make sure both days exist in the schedule
      if (!prevSchedule[dateKey1] || !prevSchedule[dateKey2]) return prevSchedule;
      
      // Create copies of the shift arrays
      const shift1Persons = [...prevSchedule[dateKey1].shifts[shiftKey1]];
      const shift2Persons = [...prevSchedule[dateKey2].shifts[shiftKey2]];
      
      // Find the positions of the persons in their respective shifts
      const index1 = shift1Persons.indexOf(personId1);
      const index2 = shift2Persons.indexOf(personId2);
      
      // If either person is not found, do nothing
      if (index1 === -1 || index2 === -1) return prevSchedule;
      
      // Swap the persons
      shift1Persons[index1] = personId2;
      shift2Persons[index2] = personId1;
      
      // Return the updated schedule
      return {
        ...prevSchedule,
        [dateKey1]: {
          ...prevSchedule[dateKey1],
          shifts: {
            ...prevSchedule[dateKey1].shifts,
            [shiftKey1]: shift1Persons
          }
        },
        [dateKey2]: {
          ...prevSchedule[dateKey2],
          shifts: {
            ...prevSchedule[dateKey2].shifts,
            [shiftKey2]: shift2Persons
          }
        }
      };
    });
  };
  
  // Update shift order after drag and drop
  const updateShiftOrder = (date: Date, shiftKey: string, newOrder: string[]) => {
    const dateKey = format(date, "yyyy-MM-dd");
    const mappedShiftKey = mapShiftLabelToKey(shiftKey) || shiftKey as "morning" | "afternoon" | "evening";
    
    setSchedule(prevSchedule => {
      // If the day doesn't exist in the schedule, do nothing
      if (!prevSchedule[dateKey]) return prevSchedule;
      
      return {
        ...prevSchedule,
        [dateKey]: {
          ...prevSchedule[dateKey],
          shifts: {
            ...prevSchedule[dateKey].shifts,
            [mappedShiftKey]: newOrder
          }
        }
      };
    });
  };
  
  // Combine all state and functions into the value object
  const value = {
    scheduleState: {
      month,
      year,
      selectedDays,
      schedule,
      serviceLimits,
      breakDays
    },
    setCurrentMonth,
    setCurrentYear,
    toggleDaySelection,
    addPersonToShift,
    removePersonFromShift,
    setServiceLimits,
    setBreakDays,
    countAssignments,
    getScheduleForDay,
    clearSchedule,
    swapPersonsInShift,
    updateShiftOrder,
    wasPersonScheduledRecently
  };
  
  return (
    <ScheduleContext.Provider value={value}>
      {children}
    </ScheduleContext.Provider>
  );
};

// Custom hook for accessing the context
export const useSchedule = () => {
  const context = useContext(ScheduleContext);
  if (context === undefined) {
    throw new Error('useSchedule must be used within a ScheduleProvider');
  }
  return context;
};
