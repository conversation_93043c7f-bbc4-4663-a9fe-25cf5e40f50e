
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, Filter } from 'lucide-react';

interface ScheduleFiltersProps {
  filters: {
    dateFrom: string;
    dateTo: string;
    shift: string;
    status: string;
  };
  onFiltersChange: (filters: any) => void;
  onClearFilters: () => void;
}

export const ScheduleFilters = ({ filters, onFiltersChange, onClearFilters }: ScheduleFiltersProps) => {
  const shifts = [
    { value: 'all', label: 'Todos os Turnos' },
    { value: 'Manhã', label: 'Manhã' },
    { value: 'Tarde', label: 'Tarde' },
    { value: 'Noite', label: 'Noite' },
    { value: 'EBD', label: '<PERSON>B<PERSON>' },
    { value: '1º Culto', label: '1º Culto' },
    { value: '2º Culto', label: '2º Culto' },
  ];

  const statuses = [
    { value: 'all', label: 'Todos os Status' },
    { value: 'confirmado', label: 'Confirmado' },
    { value: 'pendente', label: 'Pendente' },
    { value: 'cancelado', label: 'Cancelado' },
  ];

  const hasActiveFilters = filters.dateFrom || filters.dateTo || filters.shift !== 'all' || filters.status !== 'all';

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="w-5 h-5" />
          Filtros
          {hasActiveFilters && (
            <Badge variant="secondary" className="ml-2">
              Ativos
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Data Inicial</label>
            <Input
              type="date"
              value={filters.dateFrom}
              onChange={(e) => onFiltersChange({ ...filters, dateFrom: e.target.value })}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Data Final</label>
            <Input
              type="date"
              value={filters.dateTo}
              onChange={(e) => onFiltersChange({ ...filters, dateTo: e.target.value })}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Turno</label>
            <Select value={filters.shift} onValueChange={(value) => onFiltersChange({ ...filters, shift: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {shifts.map((shift) => (
                  <SelectItem key={shift.value} value={shift.value}>
                    {shift.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Status</label>
            <Select value={filters.status} onValueChange={(value) => onFiltersChange({ ...filters, status: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {hasActiveFilters && (
          <div className="flex justify-end">
            <Button variant="outline" onClick={onClearFilters} className="flex items-center gap-2">
              <X className="w-4 h-4" />
              Limpar Filtros
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
