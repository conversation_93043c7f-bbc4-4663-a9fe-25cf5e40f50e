
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar } from 'lucide-react';

interface Schedule {
  Data: string;
  Dia: string;
  Turno: string;
  Lider: string;
}

interface AdminUpcomingSchedulesProps {
  schedules: Schedule[] | undefined;
}

export const AdminUpcomingSchedules = ({ schedules }: AdminUpcomingSchedulesProps) => {
  const getWeekSchedules = () => {
    if (!schedules) return [];
    
    const today = new Date();
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 9); // Esta semana + 2 dias da próxima
    
    return schedules.filter(schedule => {
      if (!schedule.Data) return false;
      
      const dateParts = schedule.Data.split('/');
      if (dateParts.length !== 3) return false;
      
      const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
      return scheduleDate >= today && scheduleDate <= nextWeek;
    }).slice(0, 7);
  };

  const weekSchedules = getWeekSchedules();

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle>Próximas Escalas</CardTitle>
        <CardDescription>Escalas da semana e próximos 2 dias</CardDescription>
      </CardHeader>
      <CardContent>
        {weekSchedules.length > 0 ? (
          <div className="space-y-3">
            {weekSchedules.map((schedule, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Calendar className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium">{schedule.Data} - {schedule.Dia}</p>
                    <p className="text-sm text-gray-600">{schedule.Turno}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{schedule.Lider}</p>
                  <p className="text-xs text-gray-500">Líder</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Nenhuma escala programada</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
