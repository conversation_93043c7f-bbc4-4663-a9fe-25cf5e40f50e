import React from 'react';
import { useLocation } from 'react-router-dom';
import { MemberLayout } from '@/components/MemberLayout';
import { SwapRequestForm } from '@/components/swaps/SwapRequestForm';
import { AvailableSwapRequests } from '@/components/swaps/AvailableSwapRequests';
import { MemberOwnRequests } from '@/components/swaps/MemberOwnRequests';
import { useSwapRequests } from '@/hooks/useSwapRequests';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';

const MemberSwaps = () => {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const initialScheduleId = searchParams.get('scheduleId');

  const {
    swapRequests,
    allPendingSwapRequests,
    memberSchedules,
    createSwapMutation,
    isCreating
  } = useSwapRequests();

  React.useEffect(() => {
    if (swapRequests !== undefined && allPendingSwapRequests !== undefined) {
      setLoading(false);
    }
  }, [swapRequests, allPendingSwapRequests]);

  if (loading) {
    return (
      <MemberLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="w-8 h-8 animate-spin" />
        </div>
      </MemberLayout>
    );
  }

  if (error) {
    return (
      <MemberLayout>
        <div className="p-4">
          <Alert variant="destructive">
            <AlertCircle className="w-4 h-4" />
            <AlertTitle>Erro ao carregar</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </MemberLayout>
    );
  }

  const handleCreateSwap = (scheduleId: string, reason: string) => {
    if (!scheduleId || !reason.trim()) {
      return;
    }

    createSwapMutation.mutate({ scheduleId, reason });
  };

  const handleRequestUpdate = () => {
    window.location.reload();
  };

  return (
    <MemberLayout>
      <div className="space-y-6 p-4 md:p-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Trocas de Escalas</h1>
          <p className="text-gray-600 mt-2">
            Gerencie suas trocas de escalas e candidate-se para outras disponíveis
          </p>
        </div>

        <div className="space-y-6">
          <Tabs defaultValue="minhas-solicitacoes" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="minhas-solicitacoes">Minhas Solicitações</TabsTrigger>
              <TabsTrigger value="disponiveis">Disponíveis</TabsTrigger>
              <TabsTrigger value="nova-solicitacao">Nova Solicitação</TabsTrigger>
            </TabsList>

            <TabsContent value="minhas-solicitacoes" className="mt-6">
              <MemberOwnRequests
                swapRequests={swapRequests}
                onRequestUpdate={handleRequestUpdate}
              />
            </TabsContent>

            <TabsContent value="disponiveis" className="mt-6">
              <AvailableSwapRequests
                allPendingSwapRequests={allPendingSwapRequests}
                memberSchedules={memberSchedules}
              />
            </TabsContent>

            <TabsContent value="nova-solicitacao" className="mt-6">
              <div className="max-w-2xl mx-auto">
                <SwapRequestForm
                  memberSchedules={memberSchedules}
                  onCreateSwap={handleCreateSwap}
                  isCreating={isCreating}
                  initialScheduleId={initialScheduleId}
                />
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </MemberLayout>
  );
};

export default MemberSwaps;
