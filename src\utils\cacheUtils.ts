import { QueryClient } from '@tanstack/react-query';

// Debounce para invalidações de cache
const invalidationTimeouts = new Map<string, NodeJS.Timeout>();

export const debouncedInvalidateQueries = (
  queryClient: QueryClient,
  queryKey: string[],
  delay: number = 300
) => {
  const key = queryKey.join('-');
  
  // Limpar timeout anterior se existir
  if (invalidationTimeouts.has(key)) {
    clearTimeout(invalidationTimeouts.get(key)!);
  }
  
  // Criar novo timeout
  const timeout = setTimeout(() => {
    queryClient.invalidateQueries({ queryKey });
    invalidationTimeouts.delete(key);
  }, delay);
  
  invalidationTimeouts.set(key, timeout);
};

// Invalidar múltiplas queries com debounce
export const debouncedInvalidateMultipleQueries = (
  queryClient: QueryClient,
  queryKeys: string[][],
  delay: number = 300
) => {
  queryKeys.forEach(queryKey => {
    debouncedInvalidateQueries(queryClient, queryKey, delay);
  });
};

// Limpar todos os timeouts pendentes
export const clearAllInvalidationTimeouts = () => {
  invalidationTimeouts.forEach(timeout => clearTimeout(timeout));
  invalidationTimeouts.clear();
};
