import React, { createContext, useState, useContext, ReactNode, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import type { Member } from "@/types/supabaseTypes";

interface MemberAuthState {
  isAuthenticated: boolean;
  member: Member | null;
}

interface MemberAuthContextType {
  auth: MemberAuthState;
  login: (firstName: string, phone: string) => Promise<boolean>;
  logout: () => void;
  updateMember: (memberData: Partial<Member>) => void;
}

const MemberAuthContext = createContext<MemberAuthContextType | undefined>(undefined);

const MEMBER_AUTH_STORAGE_KEY = "sentinela_member_auth_state";

export const MemberAuthProvider = ({ children }: { children: ReactNode }) => {
  const [auth, setAuth] = useState<MemberAuthState>(() => {
    try {
      const storedAuth = sessionStorage.getItem(MEMBER_AUTH_STORAGE_KEY);
      return storedAuth ? JSON.parse(storedAuth) : { isAuthenticated: false, member: null };
    } catch (error) {
      console.error("Failed to parse stored member auth:", error);
      return { isAuthenticated: false, member: null };
    }
  });

  // Função para salvar o estado na sessão de forma segura
  const saveAuthState = useCallback((authState: MemberAuthState) => {
    try {
      sessionStorage.setItem(MEMBER_AUTH_STORAGE_KEY, JSON.stringify(authState));
    } catch (error) {
      console.error("Failed to save member auth state:", error);
    }
  }, []);

  useEffect(() => {
    saveAuthState(auth);
  }, [auth, saveAuthState]);

  const login = async (firstName: string, phone: string): Promise<boolean> => {
    try {
      console.log('Attempting member login:', { firstName, phone });
      
      const { data: members, error } = await supabase
        .from('membros')
        .select('*')
        .ilike('NomeCompleto', `${firstName}%`)
        .eq('Telefone', phone)
        .neq('Status', 'DESATIVADO');

      if (error) {
        console.error('Database error:', error);
        toast.error('Erro ao conectar com o banco de dados.');
        return false;
      }

      if (!members || members.length === 0) {
        toast.error('Nome ou telefone incorretos, ou membro desativado.');
        return false;
      }

      const member = members[0];
      console.log('Member found:', member);

      // Set current member for RLS policies - with better error handling
      try {
        const { error: rpcError } = await supabase.rpc('set_current_member', { 
          member_name: member['Nome Escala'] || '' 
        });
        
        if (rpcError) {
          console.error('Error setting current member:', rpcError);
          // Continue anyway as this might not be critical
        } else {
          console.log('Current member set successfully:', member['Nome Escala']);
        }
      } catch (rpcError) {
        console.error('RPC call failed:', rpcError);
        // Continue anyway
      }

      const newAuthState = {
        isAuthenticated: true,
        member: member,
      };

      setAuth(newAuthState);
      saveAuthState(newAuthState);

      toast.success(`Bem-vindo, ${member.NomeCompleto}!`);
      return true;
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Erro ao fazer login.');
      return false;
    }
  };

  const logout = useCallback(async () => {
    console.log('Logging out member...');
    
    // Clear current member setting - properly handle the promise
    try {
      await supabase.rpc('set_current_member', { member_name: '' });
      console.log('Current member cleared');
    } catch (error) {
      console.error('Error clearing current member:', error);
    }
    
    const newAuthState = {
      isAuthenticated: false,
      member: null,
    };
    
    setAuth(newAuthState);
    sessionStorage.removeItem(MEMBER_AUTH_STORAGE_KEY);
    toast.success('Logout realizado com sucesso.');
  }, []);

  const updateMember = useCallback((memberData: Partial<Member>) => {
    if (auth.member) {
      const updatedAuth = {
        ...auth,
        member: { ...auth.member, ...memberData }
      };
      setAuth(updatedAuth);
      saveAuthState(updatedAuth);
    }
  }, [auth, saveAuthState]);

  return (
    <MemberAuthContext.Provider value={{ auth, login, logout, updateMember }}>
      {children}
    </MemberAuthContext.Provider>
  );
};

export const useMemberAuth = (): MemberAuthContextType => {
  const context = useContext(MemberAuthContext);
  if (context === undefined) {
    throw new Error("useMemberAuth must be used within a MemberAuthProvider");
  }
  return context;
};