
-- <PERSON><PERSON>, vamos criar uma função de segurança para verificar se o usuário é admin
CREATE OR REPLACE FUNCTION public.is_admin_user()
RETURNS BOOLEAN AS $$
BEGIN
    -- Verifica se o email do usuário autenticado existe na tabela membros
    -- e se o cargo não é 'Membro' (ou seja, é admin)
    RETURN EXISTS (
        SELECT 1 
        FROM public.membros 
        WHERE email = (SELECT email FROM auth.users WHERE id = auth.uid())
        AND COALESCE("CargoMinisterio", '') != 'Membro'
        AND COALESCE("CargoMinisterio", '') != ''
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- Remover políticas existentes problemáticas se existirem
DROP POLICY IF EXISTS "Administradores não membros podem atualizar histórico de trocas" ON public.historico_trocas;

-- Criar nova política para permitir que administradores insiram no histórico
CREATE POLICY "Administradores podem inserir histórico de trocas" 
ON public.historico_trocas 
FOR INSERT 
TO authenticated 
WITH CHECK (public.is_admin_user());

-- Criar política para permitir que administradores leiam o histórico
CREATE POLICY "Administradores podem visualizar histórico de trocas" 
ON public.historico_trocas 
FOR SELECT 
TO authenticated 
USING (public.is_admin_user());

-- Atualizar a função do trigger para usar SECURITY DEFINER
-- Isso permite que o trigger execute com privilégios elevados
CREATE OR REPLACE FUNCTION public.registrar_historico_troca()
RETURNS TRIGGER 
LANGUAGE plpgsql
SECURITY DEFINER -- Esta é a chave para contornar RLS
AS $$
BEGIN
    -- Só registra se o status mudou para 'aceita' ou 'rejeitada'
    IF NEW.status IN ('aceita', 'rejeitada') AND (OLD.status IS NULL OR OLD.status != NEW.status) THEN
        -- Buscar dados da escala origem
        INSERT INTO public.historico_trocas (
            id_solicitacao,
            tipo_troca,
            membro_origem,
            membro_destino,
            escala_origem_data,
            escala_origem_turno,
            observacoes
        )
        SELECT 
            NEW.id,
            CASE 
                WHEN NEW.status = 'aceita' THEN 'aceita'
                ELSE 'rejeitada'
            END,
            NEW.id_solicitante,
            COALESCE(NEW.candidato_selecionado, NEW.membro_destino),
            COALESCE(ep."Data", 'Data não encontrada'),
            COALESCE(ep."Turno", 'Turno não encontrado'),
            CASE 
                WHEN NEW.status = 'aceita' THEN 
                    CONCAT('Troca aprovada pelo administrador em ', NOW()::date, 
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
                ELSE 
                    CONCAT('Troca rejeitada pelo administrador em ', NOW()::date,
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
            END
        FROM public."escalasPassadas" ep
        WHERE ep."ID" = NEW.id_escala_origem;
        
        -- Log para debugging
        RAISE NOTICE 'Histórico registrado para solicitação % com status %', NEW.id, NEW.status;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Habilitar RLS na tabela historico_trocas se ainda não estiver habilitado
ALTER TABLE public.historico_trocas ENABLE ROW LEVEL SECURITY;

-- Criar índices para melhorar performance das consultas RLS
CREATE INDEX IF NOT EXISTS idx_membros_email_cargo ON public.membros(email, "CargoMinisterio");
