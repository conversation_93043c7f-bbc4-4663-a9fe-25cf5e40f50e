export interface SwapRequest {
  id: string;
  id_solicitante: string;
  data_solicitacao: string;
  mensagem: string;
  status: 'pendente' | 'aguardando_candidatos' | 'aguardando_aprovacao' | 'aceita' | 'rejeitada';
  created_at: string;
  updated_at: string;
  data_resposta?: string;
  id_escala_origem: string;
  id_escala_destino?: string;
  membro_destino?: string;
  tipo_solicitacao: string;
  candidato_selecionado?: string;
  data_selecao_candidato?: string;
  observacoes_admin?: string;
  responsavel_aprovacao?: string;
}