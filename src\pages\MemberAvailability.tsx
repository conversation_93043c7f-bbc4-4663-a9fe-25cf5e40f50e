/** @jsxImportSource react */
import React from 'react';

// Imports de componentes
import { MemberLayout } from '@/components/MemberLayout';
import { GeneralAvailabilityDisplay } from '@/components/availability/GeneralAvailabilityDisplay';
import { UnavailabilityForm } from '@/components/availability/UnavailabilityForm';
import { UnavailabilityList } from '@/components/availability/UnavailabilityList';

// Imports de hooks
import { useMemberAvailability } from '@/hooks/useMemberAvailability';

const MemberAvailability: React.FC = () => {
  const {
    // Estados de indisponibilidade
    selectedDates,
    setSelectedDates,
    motivo,
    setMotivo,
    unavailableDates,

    // Estado de disponibilidade geral
    generalAvailability,

    // Estados de operações
    isSaving,
    unavailabilities,

    // Funções de callback
    handleSaveUnavailability,
    handleDeleteUnavailability
  } = useMemberAvailability();

  return (
    <MemberLayout>
      <div className="space-y-6 p-4 md:p-6">
        {/* Header */}
        <header>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">
            Gerenciar Disponibilidade
          </h1>
          <p className="text-gray-600 mt-2">
            Informe suas datas de indisponibilidade e gerencie sua disponibilidade geral
          </p>
        </header>

        {/* Grid principal */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Formulário de indisponibilidade */}
          <UnavailabilityForm
            selectedDates={selectedDates}
            onDatesChange={setSelectedDates}
            motivo={motivo}
            onMotivoChange={setMotivo}
            onSave={handleSaveUnavailability}
            isSaving={isSaving}
            unavailableDates={unavailableDates}
          />

          {/* Lista de indisponibilidades */}
          <UnavailabilityList
            unavailabilities={unavailabilities}
            onDelete={handleDeleteUnavailability}
          />
        </div>

        {/* Exibição de disponibilidade geral */}
        <GeneralAvailabilityDisplay 
          availability={generalAvailability} 
        />
      </div>
    </MemberLayout>
  );
};

export default MemberAvailability;