import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useMemberAuth } from '@/contexts/MemberAuthContext';

export const useMemberAvailability = () => {
  const { auth } = useMemberAuth();
  const queryClient = useQueryClient();
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [motivo, setMotivo] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Buscar indisponibilidades existentes
  const { data: unavailabilities, refetch } = useQuery({
    queryKey: ['member-unavailabilities', auth.member?.['Nome Escala']],
    queryFn: async () => {
      if (!auth.member?.['Nome Escala']) return [];
      
      console.log('Fetching unavailabilities for:', auth.member['Nome Escala']);
      
      await supabase.rpc('set_current_member', { member_name: auth.member['Nome Escala'] });
      
      const { data, error } = await supabase
        .from('disponibilidade_membros')
        .select('*')
        .eq('membro_nome', auth.member['Nome Escala'])
        .gte('data_indisponivel', new Date().toISOString().split('T')[0])
        .order('data_indisponivel', { ascending: true });
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching unavailabilities:', error);
        return [];
      }
      
      console.log('Unavailabilities fetched:', data?.length || 0);
      return data || [];
    },
    enabled: !!auth.member
  });

  // Buscar disponibilidade geral
  const { data: generalAvailability } = useQuery({
    queryKey: ['member-general-availability', auth.member?.['Nome Escala']],
    queryFn: async () => {
      if (!auth.member?.['Nome Escala']) return null;
      
      const { data, error } = await supabase
        .from('api_disponibilidade')
        .select('*')
        .eq('Nome', auth.member['Nome Escala'])
        .single();
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching availability:', error);
        return null;
      }
      return data;
    },
    enabled: !!auth.member
  });

  const handleSaveUnavailability = async () => {
    if (!auth.member?.['Nome Escala'] || selectedDates.length === 0) {
      toast.error('Selecione pelo menos uma data');
      return;
    }

    setIsSaving(true);
    try {
      console.log('Saving unavailabilities for:', auth.member['Nome Escala'], 'dates:', selectedDates);
      
      await supabase.rpc('set_current_member', { 
        member_name: auth.member['Nome Escala'] 
      });

      const unavailabilityData = selectedDates.map(date => {
        // Fix timezone issue by using the date as is
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}`;
        
        return {
          membro_nome: auth.member!['Nome Escala'],
          data_indisponivel: formattedDate,
          motivo: motivo || 'Indisponível'
        };
      });

      console.log('Inserting unavailability data:', unavailabilityData);

      const { error } = await supabase
        .from('disponibilidade_membros')
        .insert(unavailabilityData);

      if (error) {
        console.error('Error inserting unavailabilities:', error);
        toast.error('Erro ao salvar indisponibilidades: ' + error.message);
        return;
      }

      toast.success('Indisponibilidades salvas com sucesso!');
      setSelectedDates([]);
      setMotivo('');
      refetch();
    } catch (error) {
      console.error('Erro ao salvar indisponibilidades:', error);
      toast.error('Erro ao salvar indisponibilidades');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteUnavailability = async (id: string) => {
    try {
      console.log('Deleting unavailability:', id);
      
      await supabase.rpc('set_current_member', { member_name: auth.member!['Nome Escala'] });
      
      const { error } = await supabase
        .from('disponibilidade_membros')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting unavailability:', error);
        toast.error('Erro ao remover indisponibilidade: ' + error.message);
        return;
      }

      toast.success('Indisponibilidade removida!');
      refetch();
    } catch (error) {
      console.error('Erro ao remover indisponibilidade:', error);
      toast.error('Erro ao remover indisponibilidade');
    }
  };

  const unavailableDates = unavailabilities?.map(u => {
    const [year, month, day] = u.data_indisponivel.split('-').map(Number);
    return new Date(year, month - 1, day);
  }) || [];

  return {
    selectedDates,
    setSelectedDates,
    motivo,
    setMotivo,
    isSaving,
    unavailabilities,
    generalAvailability,
    unavailableDates,
    handleSaveUnavailability,
    handleDeleteUnavailability
  };
};
