import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select';
import { BarChart, Bar, XAxis, YAxis, Tooltip as RechartsTooltip, Legend } from 'recharts';
import { toast } from 'sonner';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { fetchReports, fetchPendingSwapsCount, Report, hasIncident, getNonEmptyFields } from '@/services/api';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { getVehicleChartDataByWeek, getOccurrences, getTotalAbsences, compareDates } from '@/components/dashboard/DashboardUtils';
import VehicleComparisonChart from '@/components/dashboard/VehicleComparisonChart';
import { MembersDashboard } from '@/components/dashboard/MembersDashboard';

// Month options
const months = [
  { value: '0', label: 'Janeiro' },
  { value: '1', label: 'Fevereiro' },
  { value: '2', label: 'Março' },
  { value: '3', label: 'Abril' },
  { value: '4', label: 'Maio' },
  { value: '5', label: 'Junho' },
  { value: '6', label: 'Julho' },
  { value: '7', label: 'Agosto' },
  { value: '8', label: 'Setembro' },
  { value: '9', label: 'Outubro' },
  { value: '10', label: 'Novembro' },
  { value: '11', label: 'Dezembro' }
];

const Dashboard = () => {
  const navigate = useNavigate();
  const { auth } = useAuth();
  const [expanded, setExpanded] = useState(true);
  const [hovered, setHovered] = useState(false);
  const [dashboardData, setDashboardData] = useState<Report[]>([]);
  const [filteredData, setFilteredData] = useState<Report[]>([]);
  const [pendingSwapsCount, setPendingSwapsCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [selectedMonth, setSelectedMonth] = useState<string>(new Date().getMonth().toString());
  const currentYear = new Date().getFullYear();
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const reportsPerPage = 10;
  const isMobile = useIsMobile();
  
  // Check authentication
  useEffect(() => {
    if (!auth.isAuthenticated) {
      navigate('/');
    } else {
      fetchAllData();
    }
  }, [auth.isAuthenticated, navigate]);
  
  // Fetch all dashboard data
  const fetchAllData = async () => {
    setLoading(true);
    try {
      // Fetch reports and pending swaps in parallel
      const [reports, swapsCount] = await Promise.all([
        fetchReports(),
        fetchPendingSwapsCount(),
      ]);

      // Process and set reports data
      const sortedReports = [...reports].sort(compareDates);
      setDashboardData(sortedReports);
      
      // Set pending swaps count
      setPendingSwapsCount(swapsCount);

    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast.error('Falha ao carregar dados do dashboard');
    } finally {
      setLoading(false);
    }
  };
  
  // Get weekly vehicle chart data
  const getWeeklyVehicleData = () => {
    return getVehicleChartDataByWeek(filteredData);
  };
  
  // Helper to parse dates in DD/MM/YYYY format to YYYY-MM-DD for JS Date
  const parseDate = (dateString: string): string => {
    if (!dateString) return '';
    const parts = dateString.split('/');
    if (parts.length === 3) {
      return `${parts[2]}-${parts[1]}-${parts[0]}`;
    }
    return dateString;
  };
  
  // Get total absences in the month
  const getTotalAbsencesInMonth = (): number => {
    return filteredData.reduce((total, item) => {
      const faltas = item.Faltas ? parseInt(item.Faltas) : 0;
      return total + (isNaN(faltas) ? 0 : faltas);
    }, 0);
  };
  
  // Get occurrences (filter out "NÃO. TUDO EM PAZ")
  const getOccurrencesInMonth = (): Report[] => {
    return filteredData.filter(item => {
      const alteracao = item["Alteração ?"];
      return alteracao && alteracao.toUpperCase() !== "NÃO. TUDO EM PAZ";
    });
  };
  
  // Handle report click to show details
  const handleReportClick = (report: Report) => {
    setSelectedReport(report);
    setIsReportDialogOpen(true);
  };
  
  // Filter data by month
  useEffect(() => {
    if (!dashboardData.length) {
      setFilteredData([]);
      return;
    }
    
    // If no month selected, show current month
    const monthToFilter = selectedMonth || new Date().getMonth().toString();
    
    const filtered = dashboardData.filter(item => {
      if (!item.data) return false;
      
      const dateParts = item.data.split('/');
      if (dateParts.length !== 3) return false;
      
      // O formato é DD/MM/YYYY, então o mês está no índice 1 (com base 1)
      const itemMonth = (parseInt(dateParts[1]) - 1).toString(); // Converte para base 0
      return itemMonth === monthToFilter;
    });
    
    setFilteredData(filtered);
    setCurrentPage(1); // Reset to first page when filtering
  }, [dashboardData, selectedMonth]);
  
  // Handle month change
  const handleMonthChange = (value: string) => {
    setSelectedMonth(value);
  };
  
  // Pagination functions
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const totalPages = Math.ceil(filteredData.length / reportsPerPage);
  const pageNumbers = [];
  
  // Generate page numbers for pagination
  const getPaginationItems = () => {
    const items = [];
    
    // Always show first page
    items.push(
      <PaginationItem key="page-1">
        <PaginationLink
          isActive={currentPage === 1}
          onClick={() => handlePageChange(1)}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );
    
    // Add ellipsis if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Add pages around current page
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      items.push(
        <PaginationItem key={`page-${i}`}>
          <PaginationLink
            isActive={currentPage === i}
            onClick={() => handlePageChange(i)}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    // Add ellipsis if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Always show last page if there's more than 1 page
    if (totalPages > 1) {
      items.push(
        <PaginationItem key={`page-${totalPages}`}>
          <PaginationLink
            isActive={currentPage === totalPages}
            onClick={() => handlePageChange(totalPages)}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    return items;
  };
  
  // Get current page reports
  const getCurrentPageReports = () => {
    const startIndex = (currentPage - 1) * reportsPerPage;
    const endIndex = startIndex + reportsPerPage;
    return filteredData.slice(startIndex, endIndex);
  };
  
  if (!auth.isAuthenticated) {
    return null;
  };
  
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <SidebarInset className={`transition-all duration-300 ease-in-out ${expanded || hovered ? 'ml-64' : 'ml-20'}`}>
          <div className="container mx-auto p-4 md:p-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold items-center">Dashboard</h1>
              
              </div>
              
              {isMobile && (
                <SidebarTrigger className="md:hidden">
                  <Menu className="h-5 w-5" />
                </SidebarTrigger>
              )}
            </div>
            
            <Tabs defaultValue="reports" className="space-y-6">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="reports">Relatórios do Serviço</TabsTrigger>
                <TabsTrigger value="members">Informações do Pessoal</TabsTrigger>
              </TabsList>
              
              <TabsContent value="reports" className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-semibold">Relatórios e Estatísticas</h2>
                  <div className="w-[180px]">
                    <Select value={selectedMonth} onValueChange={handleMonthChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione o mês" />
                      </SelectTrigger>
                      <SelectContent>
                        {months.map(month => (
                          <SelectItem key={month.value} value={month.value}>
                            {month.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
                    {/* Total Reports Card */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base font-medium">Total de Relatórios</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">{filteredData.length}</div>
                        <p className="text-muted-foreground text-sm mt-1">
                          neste mês
                        </p>
                      </CardContent>
                    </Card>
                    
                    {/* Occurrences Card */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base font-medium">Ocorrências Registradas</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">{getOccurrencesInMonth().length}</div>
                        <p className="text-muted-foreground text-sm mt-1">
                          neste mês
                        </p>
                      </CardContent>
                    </Card>
                    
                    {/* Faltas Count */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base font-medium">Faltas</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">
                          {getTotalAbsencesInMonth()}
                        </div>
                        <p className="text-muted-foreground text-sm mt-1">
                          total no mês
                        </p>
                      </CardContent>
                    </Card>

                    {/* Pending Swaps Card */}
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base font-medium">Trocas Pendentes</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-3xl font-bold">{pendingSwapsCount}</div>
                        <p className="text-muted-foreground text-sm mt-1">
                          aguardando resposta
                        </p>
                      </CardContent>
                    </Card>
                    
                    {/* Vehicle Comparison Chart using the weekly data */}
                    <VehicleComparisonChart data={getWeeklyVehicleData()} />
                    
                    {/* Relatórios */}
                    <Card className="col-span-full">
                      <CardHeader>
                        <CardTitle>Relatórios</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="border rounded-md overflow-hidden">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b bg-muted/50">
                                <th className="py-3 px-4 text-left font-medium">Data</th>
                                <th className="py-3 px-4 text-left font-medium">Líder</th>
                                <th className="py-3 px-4 text-left font-medium hidden md:table-cell">Culto</th>
                                <th className="py-3 px-4 text-left font-medium">Detalhes</th>
                              </tr>
                            </thead>
                            <tbody>
                              {getCurrentPageReports().length > 0 ? (
                                getCurrentPageReports().map((item) => (
                                  <tr 
                                    key={item.id} 
                                    className={`border-b hover:bg-muted/50 cursor-pointer ${
                                      hasIncident(item) ? 'bg-amber-50' : ''
                                    }`}
                                    onClick={() => {
                                      // Only open dialog for reports with incidents
                                      if (hasIncident(item)) {
                                        handleReportClick(item);
                                      }
                                    }}
                                  >
                                    <td className="py-3 px-4">{item.data}</td>
                                    <td className="py-3 px-4">{item["Nome Lider"]}</td>
                                    <td className="py-3 px-4 hidden md:table-cell">{item["Tipo Culto"]}</td>
                                    <td className="py-3 px-4">
                                      {hasIncident(item) ? (
                                        <span className="text-amber-600 font-medium">
                                          {item["Alteração ?"]}
                                        </span>
                                      ) : (
                                        <span className="text-green-600">Sem ocorrências</span>
                                      )}
                                    </td>
                                  </tr>
                                ))
                              ) : (
                                <tr>
                                  <td colSpan={4} className="py-4 px-4 text-center text-muted-foreground">
                                    Nenhum relatório registrado neste mês
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                          
                          {/* Pagination */}
                          {filteredData.length > 0 && (
                            <div className="flex justify-center p-4 border-t">
                              <Pagination>
                                <PaginationContent>
                                  {currentPage > 1 && (
                                    <PaginationItem>
                                      <PaginationPrevious onClick={() => handlePageChange(currentPage - 1)} />
                                    </PaginationItem>
                                  )}
                                  
                                  {getPaginationItems()}
                                  
                                  {currentPage < totalPages && (
                                    <PaginationItem>
                                      <PaginationNext onClick={() => handlePageChange(currentPage + 1)} />
                                    </PaginationItem>
                                  )}
                                </PaginationContent>
                              </Pagination>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="members" className="space-y-6">
                <MembersDashboard />
              </TabsContent>
            </Tabs>
          </div>
        </SidebarInset>
      </div>

      {/* Report Details Dialog */}
      <Dialog open={isReportDialogOpen} onOpenChange={setIsReportDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Detalhes do Relatório</DialogTitle>
            <DialogDescription>
              {selectedReport?.data} - {selectedReport?.["Tipo Culto"]}
            </DialogDescription>
          </DialogHeader>
          
          {selectedReport && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Líder Responsável</h3>
                  <p className="text-base">{selectedReport["Nome Lider"] || "Não informado"}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Alteração</h3>
                  <p className="text-base">{selectedReport["Alteração ?"] || "Não informado"}</p>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Carros</h3>
                  <p className="text-base">{selectedReport.carros || 0}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Motos</h3>
                  <p className="text-base">{selectedReport.motos || 0}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Bicicletas</h3>
                  <p className="text-base">{selectedReport.bikes || 0}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Faltas</h3>
                <p className="text-base">{selectedReport.Faltas || "0"}</p>
              </div>

              {/* Filter out empty fields from report */}
              {hasIncident(selectedReport) && (
                <div className="pt-2 border-t">
                  <h3 className="text-lg font-medium mb-2">Detalhes da Ocorrência</h3>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Campo</TableHead>
                        <TableHead>Valor</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {Object.entries(getNonEmptyFields(selectedReport))
                        .filter(([key]) => {
                          // Exclude common fields that are already displayed above
                          const excludedFields = ['id', 'data', 'Nome Lider', 'Tipo Culto', 'carros', 'motos', 'bikes', 'Faltas'];
                          return !excludedFields.includes(key);
                        })
                        .map(([key, value]) => (
                          <TableRow key={key}>
                            <TableCell className="font-medium">{key}</TableCell>
                            <TableCell>{value?.toString() || "-"}</TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </SidebarProvider>
  );
};

export default Dashboard;
