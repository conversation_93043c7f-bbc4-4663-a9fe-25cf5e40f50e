
import { useSwapRequestsData } from './useSwapRequestsData';
import { useScheduleData } from './useScheduleData';
import { useMemberData } from './useMemberData';

export const useMemberDashboardData = (
  memberName: string | undefined | 'admin',
  isEnabled: boolean,
  showAllBirthdays = false
) => {
  const swapRequestsData = useSwapRequestsData(memberName, isEnabled);
  const scheduleData = useScheduleData(memberName, isEnabled);
  const memberData = useMemberData(memberName, isEnabled, showAllBirthdays);

  return {
    ...swapRequestsData,
    ...scheduleData,
    ...memberData
  };
};
