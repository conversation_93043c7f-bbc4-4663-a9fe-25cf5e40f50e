
import React, { useState } from 'react';
import { CardContent } from '@/components/ui/card';
import { SwapRequest } from '@/types/swapTypes';
import { DetailedSwapCard, DetailedSwapCardContext } from './DetailedSwapCard';

interface AvailableSwapRequestsListProps {
  availableRequests: (SwapRequest & { hasDateConflict: boolean })[];
  scheduleDataMap: Record<string, any>;
  onCandidate: (requestId: string) => void;
  isCreatingCandidatura: boolean;
  selectedRequest: string | null;
  hasCandidateFor: (requestId: string) => boolean;
}

export const AvailableSwapRequestsList = ({
  availableRequests,
  scheduleDataMap,
  onCandidate,
  isCreatingCandidatura,
  selectedRequest,
  hasCandidateFor
}: AvailableSwapRequestsListProps) => {
  const [expandedId, setExpandedId] = useState<string | null>(null);

  return (
    <CardContent className="space-y-3 max-h-[500px] overflow-y-auto">
      <DetailedSwapCardContext.Provider value={{ expandedId, setExpandedId }}>
        {availableRequests.map((request) => {
        const scheduleData = scheduleDataMap[request.id_escala_origem];
        const isDisabled = hasCandidateFor(request.id) || request.hasDateConflict;
        
        return (
          <DetailedSwapCard
            key={request.id}
            request={request}
            scheduleData={scheduleData}
            candidatesCount={0}
            showCandidateButton={true}
            onCandidate={() => onCandidate(request.id)}
            isProcessing={isCreatingCandidatura && selectedRequest === request.id}
            disabled={isDisabled}
          />
        );
        })}
      </DetailedSwapCardContext.Provider>
    </CardContent>
  );
};
