
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useScheduleData = (
  memberName: string | undefined | 'admin',
  isEnabled: boolean
) => {
  // Buscar próximas escalas (apenas futuras) - só para membros específicos
  const upcomingSchedules = useQuery({
    queryKey: ['member-upcoming-schedules', memberName],
    queryFn: async () => {
      if (!memberName || memberName === 'admin') return [];
      
      console.log('Fetching upcoming schedules for:', memberName);
      
      try {
        // Buscar histórico completo para calcular dias de folga
        const { data: allSchedules, error: historyError } = await supabase
          .from('escalasPassadas')
          .select('*')
          .or(`Membro1.eq.${memberName},Membro2.eq.${memberName},Membro3.eq.${memberName},Membro4.eq.${memberName},Lider.eq.${memberName}`);
        
        if (historyError) throw historyError;
        
        const today = new Date();
        
        // Calcular dias desde última escala para cada membro
        const schedulesWithRestDays = (allSchedules || []).map(schedule => {
          if (!schedule.Data) return { ...schedule, restDays: 999 };
          
          const dateParts = schedule.Data.split('/');
          if (dateParts.length !== 3) return { ...schedule, restDays: 999 };
          
          const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
          const restDays = Math.floor((today.getTime() - scheduleDate.getTime()) / (1000 * 60 * 60 * 24));
          return { ...schedule, restDays };
        });
        
        // Ordenar por dias de folga (mais folgados primeiro)
        // Priorizando quem tem ~20 dias de folga
        const sortedSchedules = schedulesWithRestDays.sort((a, b) => {
          // Peso maior para quem está próximo de 20 dias
          const scoreA = -Math.abs(a.restDays - 20);
          const scoreB = -Math.abs(b.restDays - 20);
          return scoreB - scoreA;
        });
        
        // Filtrar apenas escalas futuras
        const futureSchedules = sortedSchedules.filter(schedule => {
          if (!schedule.Data) return false;
          
          const dateParts = schedule.Data.split('/');
          if (dateParts.length !== 3) return false;
          
          const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
          return scheduleDate >= today;
        }).slice(0, 5);
        
        console.log('Future schedules with rest days:', futureSchedules);
        return futureSchedules;
        
      } catch (error) {
        console.error('Error processing schedules:', error);
        // Fallback: busca simples ordenada por data
        const { data, error: queryError } = await supabase
          .from('escalasPassadas')
          .select('*')
          .or(`Membro1.eq.${memberName},Membro2.eq.${memberName},Membro3.eq.${memberName},Membro4.eq.${memberName},Lider.eq.${memberName}`)
          .order('Data', { ascending: true });
          
        if (queryError) {
          console.error('Error fetching schedules:', queryError);
          return [];
        }
        
        const today = new Date();
        return (data || []).filter(schedule => {
          if (!schedule.Data) return false;
          
          const dateParts = schedule.Data.split('/');
          if (dateParts.length !== 3) return false;
          
          const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
          return scheduleDate >= today;
        }).slice(0, 5);
      }
    },
    enabled: isEnabled && !!memberName && memberName !== 'admin'
  });

  return {
    upcomingSchedules: upcomingSchedules.data
  };
};
