
import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Shield, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AdminSwapAlertProps {
  pendingCount: number;
  withCandidatesCount: number;
}

export const AdminSwapAlert = ({ pendingCount, withCandidatesCount }: AdminSwapAlertProps) => {
  const navigate = useNavigate();

  if (pendingCount === 0 && withCandidatesCount === 0) return null;

  return (
    <Alert className="border-blue-200 bg-blue-50">
      <Shield className="h-4 w-4" />
      <AlertTitle className="text-blue-800">Acesso Administrativo</AlertTitle>
      <AlertDescription className="text-blue-700 space-y-2">
        <p>Há {pendingCount} solicitações de troca pendentes no sistema.</p>
        {withCandidatesCount > 0 && (
          <p className="font-medium">
            {withCandidatesCount} solicitação(ões) prontas para aprovação!
          </p>
        )}
        <Button 
          onClick={() => navigate('/admin-swap-requests')}
          size="sm"
          className="mt-2"
        >
          Gerenciar Solicitações
          <ArrowRight className="w-4 h-4 ml-1" />
        </Button>
      </AlertDescription>
    </Alert>
  );
};
