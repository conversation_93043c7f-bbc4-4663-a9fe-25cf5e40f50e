
import React from 'react';
import { CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Hand, Users } from 'lucide-react';

interface AvailableSwapRequestsHeaderProps {
  availableRequestsCount: number;
  hasRequestsAwaitingApproval: boolean;
}

export const AvailableSwapRequestsHeader = ({ 
  availableRequestsCount, 
  hasRequestsAwaitingApproval 
}: AvailableSwapRequestsHeaderProps) => {
  return (
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <Hand className="w-5 h-5" />
        Trocas Disponíveis ({availableRequestsCount})
      </CardTitle>
      <CardDescription>
        Solicitações de outros membros disponíveis para candidatura
        {hasRequestsAwaitingApproval && (
          <div className="flex items-center gap-1 mt-1 text-sm text-blue-600">
            <Users className="w-4 h-4" />
            Algumas solicitações já possuem candidatos, mas ainda aceitam mais
          </div>
        )}
      </CardDescription>
    </CardHeader>
  );
};
