import { useState } from 'react';
import { SwapRequest, ScheduleItem } from '@/types/swapTypes';
import { useSwapRequests } from '@/hooks/useSwapRequests';
import { toast } from 'react-hot-toast';
import './SwapRequestCard.css';

interface SwapRequestCardProps {
  request: SwapRequest;
  schedule?: ScheduleItem;
  candidates?: string[];
  onApprove: () => void;
  onReject: () => void;
  adminView?: boolean;
}

const SwapRequestCard = ({
request,
schedule,
candidates = [],
onApprove,
onReject,
adminView
}: SwapRequestCardProps) => {
const { approveSwapMutation } = useSwapRequests();
const [selectedCandidate, setSelectedCandidate] = useState(candidates[0] || '');
const hasCandidates = candidates.length > 0;

const handleApprove = () => {
  if (!selectedCandidate) {
    toast.error('Selecione um candidato para aprovar');
    return;
  }
  onApprove();
  approveSwapMutation.mutate({
    requestId: request.id,
    selectedCandidate
  });
};
  
  return (
    <div className="swap-request-card">
      <h3>Solicitação #{request.id.slice(0, 8)}</h3>
      <div className="request-details">
        <p><strong>Data solicitação:</strong> {new Date(request.data_solicitacao).toLocaleDateString()}</p>
        <p><strong>Membro solicitante:</strong> {request.id_solicitante}</p>
        
        {schedule && (
          <>
            <p><strong>Dia do serviço:</strong> {schedule.Dia}</p>
            <p><strong>Data do serviço:</strong> {new Date(schedule.Data).toLocaleDateString()}</p>
            <p><strong>Turno:</strong> {schedule.Turno}</p>
          </>
        )}
        
        <p><strong>Motivo:</strong> {request.mensagem}</p>
        <p><strong>Status:</strong> {request.status}</p>
        
        {hasCandidates && (
          <div className="candidates-section">
            <h4>Candidatos ({candidates.length}):</h4>
            <ul>
              {candidates.map(candidate => (
                <li key={candidate}>{candidate}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
      
      {adminView && hasCandidates && request.status === 'pendente' && (
        <div className="actions">
          <button onClick={onApprove}>Aprovar</button>
          <button onClick={onReject}>Rejeitar</button>
        </div>
      )}
    </div>
  );
};

export default SwapRequestCard;