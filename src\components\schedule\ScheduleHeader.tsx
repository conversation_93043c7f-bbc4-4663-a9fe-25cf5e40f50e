
import React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight } from 'lucide-react';

interface ScheduleHeaderProps {
  title: string;
  historyPosition: number;
  historyLength: number;
  handleUndo: () => void;
  handleRedo: () => void;
}

const ScheduleHeader: React.FC<ScheduleHeaderProps> = ({
  title,
  historyPosition,
  historyLength,
  handleUndo,
  handleRedo,
}) => {
  return (
    <header className="flex justify-between items-center py-4">
      <h1 className="text-2xl font-bold">Calend<PERSON><PERSON>sal</h1>
      
      <div className="flex items-center space-x-4">
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleUndo} 
            disabled={historyPosition <= 0}
            className="h-8 w-8"
            title="Voltar"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <span className="text-xs text-muted-foreground">
            {historyPosition + 1}/{historyLength || 1}
          </span>
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleRedo}
            disabled={historyPosition >= historyLength - 1}
            className="h-8 w-8"
            title="Avançar"
          >
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </header>
  );
};

export default ScheduleHeader;
