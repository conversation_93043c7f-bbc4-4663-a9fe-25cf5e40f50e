
import { useRef } from 'react';
import html2canvas from 'html2canvas';

export function useScheduleImage() {
  const calendarRef = useRef<HTMLDivElement>(null);
  
  const captureCalendarImage = async (): Promise<string | null> => {
    if (!calendarRef.current) return null;
    
    try {
      // Add temporary padding to the element before capture
      const originalPadding = calendarRef.current.style.padding;
      calendarRef.current.style.padding = '30px'; // Increased padding for better margins
      
      // Make sure logos and header are visible during capture
      const logos = document.querySelectorAll('.schedule-logos img');
      logos.forEach(logo => {
        if (logo instanceof HTMLElement) {
          logo.style.display = 'inline-block';
          logo.style.visibility = 'visible';
          logo.style.opacity = '1';
        }
      });
      
      // Ensure the header is fully visible
      const headers = calendarRef.current.querySelectorAll('h1, h2, h3, h4, h5, h6');
      headers.forEach(header => {
        if (header instanceof HTMLElement) {
          header.style.visibility = 'visible';
          header.style.display = 'block';
        }
      });
      
      // Temporarily remove highlighting from recently assigned persons
      const highlightedElements = calendarRef.current.querySelectorAll('.bg-yellow-100, .bg-amber-100');
      highlightedElements.forEach(element => {
        if (element instanceof HTMLElement) {
          element.style.backgroundColor = 'transparent';
        }
      });
      
      // Find the closest parent element with the schedule-logos class
      const logosContainer = document.querySelector('.schedule-logos');
      let element = calendarRef.current;
      
      // If we have a logos container, we want to include it in our capture
      if (logosContainer) {
        // Create a temporary wrapper for capturing both logos and calendar
        const wrapper = document.createElement('div');
        wrapper.style.background = 'white';
        wrapper.style.padding = '30px';
        wrapper.style.position = 'absolute';
        wrapper.style.left = '-9999px'; // Position offscreen
        wrapper.style.width = `${calendarRef.current.scrollWidth}px`;
        
        // Clone the logos to our wrapper
        const logosClone = logosContainer.cloneNode(true);
        wrapper.appendChild(logosClone);
        
        // Clone the calendar to our wrapper
        const calendarClone = calendarRef.current.cloneNode(true);
        wrapper.appendChild(calendarClone);
        
        // Add to body, capture, then remove
        document.body.appendChild(wrapper);
        
        const canvas = await html2canvas(wrapper, {
          scale: 2, // Higher scale for better quality
          logging: false,
          useCORS: true,
          backgroundColor: '#ffffff',
          windowWidth: wrapper.scrollWidth * 2,
          windowHeight: wrapper.scrollHeight * 2,
          onclone: (clonedDoc, element) => {
            // Additional styling for the cloned element
            if (element instanceof HTMLElement) {
              element.style.backgroundColor = 'white';
              
              // Ensure the schedule logos are visible in the cloned document
              const clonedLogosContainer = clonedDoc.querySelector('.schedule-logos');
              if (clonedLogosContainer instanceof HTMLElement) {
                clonedLogosContainer.style.display = 'flex';
                clonedLogosContainer.style.justifyContent = 'center';
                clonedLogosContainer.style.marginBottom = '20px';
              }
              
              // Remove any yellow highlighting from the cloned document
              const highlightedItems = clonedDoc.querySelectorAll('.bg-yellow-100, .bg-amber-100');
              highlightedItems.forEach(item => {
                if (item instanceof HTMLElement) {
                  item.style.backgroundColor = 'transparent';
                }
              });
            }
          }
        });
        
        document.body.removeChild(wrapper);
        
        // Restore original padding
        calendarRef.current.style.padding = originalPadding;
        
        return canvas.toDataURL('image/png');
      } else {
        // Fallback to just capturing the calendar if logos aren't found
        const canvas = await html2canvas(calendarRef.current, {
          scale: 2, // Higher scale for better quality
          logging: false,
          useCORS: true,
          backgroundColor: '#ffffff',
          windowWidth: calendarRef.current.scrollWidth * 2,
          windowHeight: calendarRef.current.scrollHeight * 2,
          onclone: (clonedDoc, element) => {
            // Additional styling for the cloned element
            if (element instanceof HTMLElement) {
              element.style.backgroundColor = 'white';
              element.style.borderRadius = '8px';
              element.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.1)';
              
              // Remove any yellow highlighting from the cloned document
              const highlightedItems = clonedDoc.querySelectorAll('.bg-yellow-100, .bg-amber-100');
              highlightedItems.forEach(item => {
                if (item instanceof HTMLElement) {
                  item.style.backgroundColor = 'transparent';
                }
              });
            }
          }
        });
        
        // Restore original padding
        calendarRef.current.style.padding = originalPadding;
        
        return canvas.toDataURL('image/png');
      }
    } catch (error) {
      console.error("Failed to capture calendar image:", error);
      return null;
    }
  };
  
  return { calendarRef, captureCalendarImage };
}
