
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSchedule } from '@/contexts/ScheduleContext';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/AppSidebar';
import { generateScheduleTitle } from '@/utils/scheduleUtils';
import { fetchPersons } from '@/services/api';
import { Person, ScheduleData } from '@/types';
import { Button } from '@/components/ui/button';
import { EyeIcon, EyeOffIcon } from 'lucide-react';

// Custom hooks
import { useScheduleHistory } from '@/hooks/useScheduleHistory';
import { useScheduleSwap } from '@/hooks/useScheduleSwap';
import { useScheduleImage } from '@/hooks/useScheduleImage';

// Components
import ScheduleHeader from '@/components/schedule/ScheduleHeader';
import ScheduleLogos from '@/components/schedule/ScheduleLogos';
import ScheduleTable from '@/components/schedule/ScheduleTable';
import ScheduleFooter from '@/components/schedule/ScheduleFooter';
import SwapDialog from '@/components/schedule/SwapDialog';
import { Loading } from '@/components/ui/loading';

const ScheduleView = () => {
  const navigate = useNavigate();
  const { scheduleState, swapPersonsInShift, clearSchedule } = useSchedule();
  const { auth } = useAuth();
  const [persons, setPersons] = useState<Person[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [scheduleData, setScheduleData] = useState<ScheduleData[]>([]);
  const [showHighlighted, setShowHighlighted] = useState(true);
  
  // Custom hooks
  const { calendarRef, captureCalendarImage } = useScheduleImage();
  const { 
    history, 
    historyPosition, 
    saveToHistory, 
    handleUndo, 
    handleRedo 
  } = useScheduleHistory(scheduleState);
  
  const { 
    showSwapDialog, 
    setShowSwapDialog, 
    selectedPersons, 
    handlePersonClick, 
    handleSwapConfirm, 
    handleSwapCancel 
  } = useScheduleSwap({
    onSwapConfirm: swapPersonsInShift,
    saveToHistory,
    scheduleState
  });
  
  // Check authentication with a more persistent approach
  useEffect(() => {
    const checkAuth = () => {
      if (!auth.isAuthenticated) {
        // Check localStorage for auth token before navigating
        const hasToken = localStorage.getItem('auth_token');
        if (!hasToken) {
          navigate('/login', { replace: true });
        }
      }
    };
    
    checkAuth();
    // Add event listener to check auth on window focus
    window.addEventListener('focus', checkAuth);
    
    return () => {
      window.removeEventListener('focus', checkAuth);
    };
  }, [auth.isAuthenticated, navigate]);

  // Check if we have selected days, otherwise redirect to calendar
  useEffect(() => {
    if (!scheduleState.selectedDays || scheduleState.selectedDays.length === 0) {
      navigate('/calendar');
      toast.error('Selecione dias para gerar a escala');
    }
  }, [scheduleState.selectedDays, navigate]);

  // Load persons data with better error handling
  useEffect(() => {
    const loadPersons = async () => {
      try {
        setLoading(true);
        const data = await fetchPersons();
        setPersons(Array.isArray(data) ? data : []);
      } catch (error) {
        console.error("Failed to load persons:", error);
        toast.error("Falha ao carregar os dados de pessoas");
        setPersons([]);
      } finally {
        setLoading(false);
      }
    };
    
    loadPersons();
  }, []);

  // Prepare schedule data for endpoint
  useEffect(() => {
    if (!persons || persons.length === 0 || !scheduleState.selectedDays || scheduleState.selectedDays.length === 0) return;
    
    let id = 239;
    const data: ScheduleData[] = [];
    
    // Sort selected days chronologically
    const sortedDays = [...scheduleState.selectedDays].sort((a, b) => a.getTime() - b.getTime());
    
    sortedDays.forEach(day => {
      const dateKey = format(day, "yyyy-MM-dd");
      const daySchedule = scheduleState.schedule[dateKey];
      
      if (!daySchedule) return;
      
      const dayOfWeek = day.toLocaleDateString('pt-BR', { weekday: 'long' });
      const formattedDate = format(day, "dd/MM/yyyy");
      
      // Get shift names based on day of week
      let shiftNames: string[] = [];
      if (dayOfWeek === "domingo") {
        shiftNames = ["EBD", "1º Culto", "2º Culto"];
      } else {
        shiftNames = ["Manhã", "Tarde", "Noite"];
      }
      
      // Map shifts
      const shifts = ["morning", "afternoon", "evening"];
      
      shifts.forEach((shift, index) => {
        const shiftName = shiftNames[index];
        const personIds = daySchedule.shifts[shift as "morning" | "afternoon" | "evening"];
        
        if (personIds && personIds.length > 0) {
          // Get person names for IDs
          const personNames = personIds.map(id => {
            const person = persons.find(p => p.ID === id);
            return person ? person.Nome : "Pessoa não encontrada";
          }).filter(name => name !== "Pessoa não encontrada" && name.trim() !== "");
          
          if (personNames.length === 0) return; // Skip if no valid names
          
          // Get shift times
          let startTime = "00:00";
          let endTime = "00:00";
          
          switch(shiftName) {
            case 'Manhã':
            case 'EBD':
              startTime = '08:00';
              endTime = '12:00';
              break;
            case 'Tarde':
            case '1º Culto':
              startTime = '12:00';
              endTime = '18:00';
              break;
            case 'Noite':
            case '2º Culto':
              startTime = '18:00';
              endTime = '22:00';
              break;
          }
          
          // Create schedule data entry
          const scheduleEntry: ScheduleData = {
            ID: `ID-${id++}`,
            Dia: dayOfWeek.charAt(0).toUpperCase() + dayOfWeek.slice(1),
            Data: formattedDate,
            Turno: shiftName,
            "Start time": startTime,
            "End time": endTime,
            Lider: personNames[0] || "",
            Membro1: personNames[1] || "",
            Membro2: personNames[2] || "",
            Membro3: personNames[3] || "",
            Membro4: personNames[4] || "",
            Membro5: personNames[5] || "",
            Membro6: personNames[6] || "",
            Membro7: personNames[7] || "",
            Membro8: personNames[8] || "",
            Membro9: personNames[9] || "",
          };
          
          data.push(scheduleEntry);
        }
      });
    });
    
    setScheduleData(data);
  }, [persons, scheduleState.schedule, scheduleState.selectedDays]);

  const sendDataToEndpoint = async () => {
    if (scheduleData.length === 0) {
      toast.error("Nenhum dado para enviar");
      return;
    }
    
    setSending(true);
    
    try {
      // Make sure the ScheduleLogos are visible in the DOM for capture
      const logosElement = document.querySelector('.schedule-logos');
      if (logosElement) {
        (logosElement as HTMLElement).style.display = 'flex';
      }
      
      // Capture calendar image as base64
      const calendarImage = await captureCalendarImage();
      
      // Extract year from the first date in scheduleData
      const firstDateStr = scheduleData[0]?.Data;
      let scheduleYear = new Date().getFullYear(); // Default to current year
      
      if (firstDateStr) {
        const dateParts = firstDateStr.split('/');
        if (dateParts.length === 3) {
          scheduleYear = parseInt(dateParts[2]);
        }
      }
      
      // Create a title using the utility function
      const scheduleTitle = generateScheduleTitle(scheduleState.selectedDays);
      
      // Create metadata for the schedule
      const scheduleMetadata = {
        id: `sch-${Date.now()}`,
        title: scheduleTitle,
        date: format(new Date(), 'yyyy-MM-dd'),
        days: scheduleState.selectedDays,
        createdAt: format(new Date(), 'yyyy-MM-dd'),
        createdBy: auth.username || 'Usuário',
        status: 'active',
        year: scheduleYear
      };
      
      const payload = {
        scheduleData,
        scheduleMetadata,
        calendarImage
      };
      
      console.log("Sending schedule data:", JSON.stringify(payload));
      
      // Save the schedule to both local storage and webhook
      try {
        // First, get existing schedules or initialize an empty array
        const existingSchedulesStr = localStorage.getItem('saved_schedules');
        const existingSchedules = existingSchedulesStr ? JSON.parse(existingSchedulesStr) : [];
        
        // Add the new schedule with complete data
        const completeSchedule = {
          ...scheduleMetadata,
          scheduleData,
          calendarImage
        };
        
        existingSchedules.unshift(completeSchedule); // Add to beginning of array
        
        // Save back to localStorage
        localStorage.setItem('saved_schedules', JSON.stringify(existingSchedules));
        
        console.log("Schedule saved to local storage", completeSchedule);
      } catch (storageError) {
        console.error("Failed to save to localStorage:", storageError);
      }
      
      // Now send to webhook
      const response = await fetch("https://webhook.dpscloud.online/webhook/0c57ab5f-dc27-467f-a877-955dd5c915b8", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(payload)
      });
      
      if (response.ok) {
        toast.success("Escala salva com sucesso");
        // Clear schedule and return to past schedules page
        clearSchedule();
        navigate('/past-schedules'); // Navigate to past schedules after saving
      } else {
        const errorText = await response.text();
        console.error("Error response:", errorText);
        throw new Error(`Erro ao enviar dados: ${response.status}`);
      }
    } catch (error) {
      console.error("Erro ao enviar dados:", error);
      toast.error("Falha ao salvar escala. Tente novamente.");
    } finally {
      setSending(false);
    }
  };

  const handleBack = () => {
    navigate('/calendar');
  };

  // Toggle highlighting function
  const toggleHighlighting = () => {
    setShowHighlighted(!showHighlighted);
    toast.info(showHighlighted ? "Ocultando entradas repetidas" : "Mostrando entradas repetidas");
  };

  // Calculate max number of people in any shift for consistent row heights
  const calculateMaxPeopleCount = () => {
    let maxCount = 0;
    
    if (!scheduleState.selectedDays) return maxCount;
    
    scheduleState.selectedDays.forEach(day => {
      const dateKey = format(day, "yyyy-MM-dd");
      const daySchedule = scheduleState.schedule[dateKey];
      
      if (daySchedule) {
        Object.values(daySchedule.shifts).forEach(persons => {
          if (Array.isArray(persons) && persons.length > maxCount) {
            maxCount = persons.length;
          }
        });
      }
    });
    
    // Ensure a minimum height for empty shifts
    return Math.max(maxCount, 3);
  };

  // Maximum number of people in any shift for consistent heights
  const maxPeopleCount = calculateMaxPeopleCount();
  
  // Sort days chronologically
  const sortedDays = scheduleState.selectedDays ? 
    [...scheduleState.selectedDays].sort((a, b) => a.getTime() - b.getTime()) : [];

  if (!auth.isAuthenticated) {
    // Check for local auth token before rendering nothing
    const hasToken = localStorage.getItem('auth_token');
    if (!hasToken) {
      return null;
    }
  }

  if (loading) {
    return <Loading text="Carregando dados..." />;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />
        <SidebarInset>
          <div className="container max-w-7xl mx-auto space-y-6 p-4">
            <ScheduleHeader 
              title="Escala de Serviço"
              historyPosition={historyPosition}
              historyLength={history.length}
              handleUndo={handleUndo}
              handleRedo={handleRedo}
            />

            <div className="flex justify-between items-center mb-2">
              <ScheduleLogos />
              
              <Button
                variant="outline"
                size="sm"
                onClick={toggleHighlighting}
                className="flex items-center gap-2"
              >
                {showHighlighted ? (
                  <>
                    <EyeOffIcon className="h-4 w-4" />
                    Ocultar repetidos
                  </>
                ) : (
                  <>
                    <EyeIcon className="h-4 w-4" />
                    Mostrar repetidos
                  </>
                )}
              </Button>
            </div>

            <div className="print-optimized">
              <ScheduleTable
                calendarRef={calendarRef}
                sortedDays={sortedDays}
                schedule={scheduleState.schedule}
                persons={persons}
                selectedPersons={selectedPersons}
                maxPeopleCount={maxPeopleCount}
                onPersonClick={handlePersonClick}
                showHighlightedEntries={showHighlighted}
              />
            </div>

            <ScheduleFooter 
              handleBack={handleBack}
              handleSave={sendDataToEndpoint}
              sending={sending}
              disabled={scheduleData.length === 0}
            />
          </div>
        </SidebarInset>

        <SwapDialog 
          open={showSwapDialog} 
          onOpenChange={setShowSwapDialog}
          onConfirm={handleSwapConfirm}
          onCancel={handleSwapCancel}
          person1Name={selectedPersons?.name1}
          person2Name={selectedPersons?.name2}
        />
        
        {sending && <Loading text="Salvando escala..." />}
      </div>
    </SidebarProvider>
  );
};

export default ScheduleView;
