
export interface SwapRequest {
  id: string;
  id_solicitante: string;
  data_solicitacao: string;
  mensagem: string;
  status: 'pendente' | 'aguardando_candidatos' | 'aguardando_aprovacao' | 'aceita' | 'rejeitada' | 'cancelada';
  created_at: string;
  updated_at: string;
  data_resposta?: string;
  id_escala_origem: string;
  id_escala_destino?: string;
  membro_destino?: string;
  tipo_solicitacao: string;
  candidato_selecionado?: string;
  data_selecao_candidato?: string;
  observacoes_admin?: string;
  responsavel_aprovacao?: string;
  scheduleData?: ScheduleItem;
  candidaturas?: Array<{
    id: string;
    membro_candidato: string;
    status: string;
    created_at: string;
  }>;
}

export interface ScheduleItem {
  ID: string;
  Data: string;
  Turno: string;
  Dia: string;
  Lider: string;
  Membro1?: string;
  Membro2?: string;
  Membro3?: string;
  Membro4?: string;
}

export interface AvailableMember {
  Nome: string;
  'Segunda-Feira': string;
  'Terça-Feira': string;
  'Quarta-Feira': string;
  'Quinta-Feira': string;
  'Sexta-Feira': string;
  Sabado: string;
  Domingo: string;
}
