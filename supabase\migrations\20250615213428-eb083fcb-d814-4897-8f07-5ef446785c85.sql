
-- <PERSON><PERSON>r o trigger existente para registrar histórico automaticamente
DROP TRIGGER IF EXISTS trigger_historico_troca ON public.solicitacoes_troca;
DROP FUNCTION IF EXISTS public.registrar_historico_troca();

-- Criar função melhorada para registrar histórico
CREATE OR REPLACE FUNCTION public.registrar_historico_troca()
RETURNS TRIGGER AS $$
BEGIN
    -- Só registra se o status mudou para 'aceita' ou 'rejeitada'
    IF NEW.status IN ('aceita', 'rejeitada') AND (OLD.status IS NULL OR OLD.status != NEW.status) THEN
        -- Buscar dados da escala origem
        INSERT INTO public.historico_trocas (
            id_solicitacao,
            tipo_troca,
            membro_origem,
            membro_destino,
            escala_origem_data,
            escala_origem_turno,
            observacoes
        )
        SELECT 
            NEW.id,
            CASE 
                WHEN NEW.status = 'aceita' THEN 'aceita'
                ELSE 'rejeitada'
            END,
            NEW.id_solicitante,
            COALESCE(NEW.candidato_selecionado, NEW.membro_destino),
            COALESCE(ep."Data", 'Data não encontrada'),
            COALESCE(ep."Turno", 'Turno não encontrado'),
            CASE 
                WHEN NEW.status = 'aceita' THEN 
                    CONCAT('Troca aprovada pelo administrador em ', NOW()::date, 
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
                ELSE 
                    CONCAT('Troca rejeitada pelo administrador em ', NOW()::date,
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
            END
        FROM public."escalasPassadas" ep
        WHERE ep."ID" = NEW.id_escala_origem;
        
        -- Log para debugging
        RAISE NOTICE 'Histórico registrado para solicitação % com status %', NEW.id, NEW.status;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recriar o trigger
CREATE TRIGGER trigger_historico_troca
    AFTER UPDATE ON public.solicitacoes_troca
    FOR EACH ROW
    EXECUTE FUNCTION public.registrar_historico_troca();

-- Adicionar índices para melhorar performance
CREATE INDEX IF NOT EXISTS idx_solicitacoes_troca_status ON public.solicitacoes_troca(status);
CREATE INDEX IF NOT EXISTS idx_solicitacoes_troca_solicitante ON public.solicitacoes_troca(id_solicitante);
CREATE INDEX IF NOT EXISTS idx_historico_trocas_solicitacao ON public.historico_trocas(id_solicitacao);
