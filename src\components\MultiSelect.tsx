
import * as React from "react";
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Command, CommandGroup, CommandItem } from "@/components/ui/command";
import { Command as CommandPrimitive } from "cmdk";

interface Option {
  value: string;
  label: string;
  highlight?: boolean;
}

interface MultiSelectProps {
  options: Option[];
  selectedValues: Option[] | string[];
  onChange: (selectedValues: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  maxItems?: number;
  highlightRecent?: boolean;
}

export function MultiSelect({
  options,
  selectedValues,
  onChange,
  placeholder = "Select options",
  disabled = false,
  maxItems,
  highlightRecent = false,
}: MultiSelectProps) {
  const inputRef = React.useRef<HTMLInputElement>(null);
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");

  // Convert simple string array to Option[] if needed
  const normalizedSelectedValues = React.useMemo(() => {
    if (!selectedValues || selectedValues.length === 0) return [];
    
    if (typeof selectedValues[0] === "string") {
      return (selectedValues as string[]).map(value => {
        const option = options.find(opt => opt.value === value);
        return option || { value, label: value };
      });
    }
    
    return selectedValues as Option[];
  }, [selectedValues, options]);

  // Get selected values as strings
  const selectedValuesAsStrings = normalizedSelectedValues.map(item => item.value);

  const handleUnselect = (value: string) => {
    onChange(selectedValuesAsStrings.filter(item => item !== value));
  };

  const handleSelect = (value: string) => {
    // Check if maximum items reached
    if (maxItems && normalizedSelectedValues.length >= maxItems) {
      return;
    }
    
    if (selectedValuesAsStrings.includes(value)) {
      handleUnselect(value);
    } else {
      onChange([...selectedValuesAsStrings, value]);
    }
  };

  // Filter options based on input
  const filteredOptions = React.useMemo(() => {
    return inputValue.length > 0
      ? options.filter(option =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      : options;
  }, [inputValue, options]);

  return (
    <div className="relative">
      <div
        className={`flex min-h-10 w-full flex-wrap items-center rounded-md border border-input px-3 py-2 text-sm ring-offset-background ${
          disabled ? "bg-muted cursor-not-allowed" : "bg-background cursor-pointer"
        }`}
        onClick={() => {
          if (!disabled) {
            setOpen(true);
            inputRef.current?.focus();
          }
        }}
      >
        {normalizedSelectedValues.length > 0 && (
          <div className="flex flex-wrap gap-1 mr-2">
            {normalizedSelectedValues.map(item => (
              <Badge
                key={item.value}
                className={`${
                  item.highlight && highlightRecent ? "bg-amber-300 hover:bg-amber-400 text-black" : ""
                }`}
                variant="secondary"
              >
                {item.label}
                <button
                  className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      handleUnselect(item.value);
                    }
                  }}
                  onMouseDown={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleUnselect(item.value);
                  }}
                >
                  <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                </button>
              </Badge>
            ))}
          </div>
        )}
        <CommandPrimitive>
          <div className="flex-1">
            <CommandPrimitive.Input
              ref={inputRef}
              value={inputValue}
              onValueChange={setInputValue}
              onBlur={() => setOpen(false)}
              onFocus={() => setOpen(true)}
              placeholder={normalizedSelectedValues.length === 0 ? placeholder : ""}
              disabled={disabled}
              className="w-full bg-transparent focus:outline-none disabled:cursor-not-allowed"
            />
          </div>
          {open && (
            <CommandPrimitive.List className="absolute z-10 w-full top-full mt-1 rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in">
              {filteredOptions.length > 0 ? (
                <CommandGroup className="h-full overflow-auto p-1">
                  {filteredOptions.map(option => {
                    const isSelected = selectedValuesAsStrings.includes(option.value);
                    return (
                      <CommandItem
                        key={option.value}
                        value={option.label}
                        onMouseDown={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onSelect={() => handleSelect(option.value)}
                        className={`flex items-center gap-2 ${isSelected ? "bg-accent" : ""} ${
                          option.highlight && highlightRecent ? "bg-amber-300" : ""
                        }`}
                      >
                        <div
                          className={`border mr-2 flex h-4 w-4 shrink-0 items-center justify-center rounded-sm ${
                            isSelected ? "bg-primary border-primary text-primary-foreground" : "opacity-50 border-muted-foreground"
                          }`}
                        >
                          {isSelected && <span className="h-2 w-2 rounded-full bg-current" />}
                        </div>
                        <span>{option.label}</span>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              ) : (
                <div className="py-6 text-center text-sm">Nenhuma opção encontrada.</div>
              )}
            </CommandPrimitive.List>
          )}
        </CommandPrimitive>
      </div>
    </div>
  );
}
