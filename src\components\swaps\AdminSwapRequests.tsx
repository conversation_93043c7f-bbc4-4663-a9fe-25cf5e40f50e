import { useQuery } from '@tanstack/react-query';
import { SwapRequest } from '@/types/supabaseTypes';
import { fetchAllSwapRequests } from '@/services/swapService';
import AdminSwapCard from './AdminSwapCard';
import StatusFilter from './StatusFilter';
import { useState } from 'react';

export default function AdminSwapRequests() {
  const [statusFilter, setStatusFilter] = useState<string>('all');
  
  const { data: swapRequests, isLoading, error } = useQuery<SwapRequest[]>({
    queryKey: ['admin-swap-requests', statusFilter],
    queryFn: () => fetchAllSwapRequests(statusFilter),
  });

  if (isLoading) return <div>Carregando solicitações...</div>;
  if (error) return <div>Erro ao carregar solicitações</div>;

  return (
    <div className="space-y-4">
      <StatusFilter 
        currentFilter={statusFilter}
        onFilterChange={setStatusFilter}
      />
      
      <div className="grid gap-4">
        {swapRequests?.map(request => (
          <AdminSwapCard 
            key={request.id} 
            request={request}
          />
        ))}
      </div>
    </div>
  );
}