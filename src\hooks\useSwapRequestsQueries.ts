
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { useAuth } from '@/contexts/AuthContext';
import { SwapRequest, ScheduleItem, AvailableMember } from '@/types/swapTypes';
import { fetchSwapRequestsWithScheduleData } from '@/services/swapService';
import { 
  fetchSwapsWithCandidates, 
  fetchSwapsAwaitingCandidates,
  fetchApprovedSwapsCount 
} from '@/services/swapStatsService';

export const useSwapRequestsQueries = () => {
  const { auth: memberAuth } = useMemberAuth();
  const { auth: adminAuth } = useAuth();

  // Determinar se é admin ou membro
  const isAdmin = adminAuth.isAuthenticated;
  const isMember = !!memberAuth.member;
  const memberName = memberAuth.member?.['Nome Escala'];

  console.log('useSwapRequestsQueries - isAdmin:', isAdmin);
  console.log('useSwapRequestsQueries - isMember:', isMember);
  console.log('useSwapRequestsQueries - memberName:', memberName);

  // Buscar solicitações de troca do membro autenticado (apenas para membros)
  const {
    data: swapRequests,
    error: swapRequestsError,
    isFetching: isFetchingSwapRequests
  } = useQuery({
    queryKey: ['swap-requests', memberName],
    queryFn: async (): Promise<SwapRequest[]> => {
      if (!memberName) {
        throw new Error('Membro não autenticado');
      }

      try {
        await supabase.rpc('set_current_member', { member_name: memberName });

        console.log('Fetching swap requests for member:', memberName);
        
        // Filtrar apenas escalas do mês atual e futuros
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        const currentYear = currentDate.getFullYear();
        
        const { data, error } = await supabase
          .from('solicitacoes_troca')
          .select(`
            *,
            escalasPassadas!inner("Data", "Turno", "Dia")
          `)
          .eq('id_solicitante', memberName)
          .gte('escalasPassadas.Data', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`)
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }

        console.log('Received swap requests:', data);
        return (data as unknown as SwapRequest[]) || [];
      } catch (error) {
        console.error('Error fetching swap requests:', error);
        throw new Error('Falha ao carregar solicitações de troca');
      }
    },
    enabled: isMember && !isAdmin,
    retry: 2,
  });

  // Buscar todas as solicitações com dados de escala (para admin)
  const {
    data: allSwapRequestsWithSchedule,
    error: allSwapError,
    isFetching: isFetchingAllSwaps
  } = useQuery({
    queryKey: ['all-swap-requests-with-schedule'],
    queryFn: async () => {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();
      
      return await fetchSwapRequestsWithScheduleData(currentYear, currentMonth);
    },
    enabled: isAdmin,
    retry: 2,
    refetchInterval: 30000, // Atualizar a cada 30 segundos para admin
  });

  // Buscar todas as solicitações de troca disponíveis para candidatura (apenas para membros)
  const {
    data: allPendingSwapRequests,
    error: allPendingError,
    isFetching: isFetchingPending
  } = useQuery({
    queryKey: ['all-pending-swap-requests'],
    queryFn: async (): Promise<SwapRequest[]> => {
      try {
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth() + 1;
        const currentYear = currentDate.getFullYear();

        const { data, error } = await supabase
          .from('solicitacoes_troca')
          .select(`
            *,
            escalasPassadas!inner("Data", "Turno", "Dia")
          `)
          .or('status.eq.aguardando_candidatos,status.eq.aguardando_aprovacao,status.eq.pendente')
          .gte('escalasPassadas.Data', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`)
          .order('created_at', { ascending: false });

        if (error) {
          throw error;
        }
        return (data as unknown as SwapRequest[]) || [];
      } catch (error) {
        console.error('Error fetching all pending swap requests:', error);
        throw new Error('Falha ao carregar solicitações pendentes');
      }
    },
    enabled: isMember && !isAdmin,
    retry: 2,
  });

  // Buscar contagem de trocas com candidatos (para admin)
  const { data: swapsWithCandidatesCount } = useQuery({
    queryKey: ['swaps-with-candidates-count'],
    queryFn: fetchSwapsWithCandidates,
    enabled: isAdmin,
    refetchInterval: 30000,
  });

  // Buscar contagem de trocas aguardando candidatos (para admin)
  const { data: swapsAwaitingCandidatesCount } = useQuery({
    queryKey: ['swaps-awaiting-candidates-count'],
    queryFn: fetchSwapsAwaitingCandidates,
    enabled: isAdmin,
    refetchInterval: 30000,
  });

  // Buscar contagem de trocas aprovadas (para admin)
  const { data: approvedSwapsCount } = useQuery({
    queryKey: ['approved-swaps-count'],
    queryFn: fetchApprovedSwapsCount,
    enabled: isAdmin,
    refetchInterval: 30000,
  });

  // Buscar escalas do membro autenticado (apenas para membros)
  const { data: memberSchedules } = useQuery({
    queryKey: ['member-schedules-for-swap', memberName],
    queryFn: async (): Promise<ScheduleItem[]> => {
      if (!memberName) return [];

      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();

      const { data, error } = await supabase
        .from('escalasPassadas')
        .select('*')
        .or(
          `Lider.eq.${memberName},Membro1.eq.${memberName},Membro2.eq.${memberName},Membro3.eq.${memberName},Membro4.eq.${memberName}`,
        )
        .gte('Data', `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`);

      if (error) {
        console.error('Error fetching member schedules:', error);
        return [];
      }
      return data || [];
    },
    enabled: isMember,
  });

  // Buscar membros disponíveis
  const { data: availableMembers } = useQuery({
    queryKey: ['available-members'],
    queryFn: async (): Promise<AvailableMember[]> => {
      const { data, error } = await supabase.from('disponibilidade').select('*');
      if (error) {
        console.error('Error fetching available members:', error);
        return [];
      }
      return (data || []).map((item: any) => ({
        Nome: item["Quem atualizou a Escala"] || item["proprietário"] || "",
        "Segunda-Feira": item["Segunda-Feira"] || "",
        "Terça-Feira": item["Terça-Feira"] || "",
        "Quarta-Feira": item["Quarta-Feira"] || "",
        "Quinta-Feira": item["Quinta-Feira"] || "",
        "Sexta-Feira": item["Sexta-Feira"] || "",
        Sabado: item["Sabado"] || "",
        Domingo: item["Domingo"] || "",
      }));
    },
    enabled: isAdmin || isMember,
  });

  return {
    swapRequests,
    allPendingSwapRequests,
    allSwapRequestsWithSchedule,
    memberSchedules,
    availableMembers,
    swapsWithCandidatesCount: swapsWithCandidatesCount || 0,
    swapsAwaitingCandidatesCount: swapsAwaitingCandidatesCount || 0,
    approvedSwapsCount: approvedSwapsCount || 0,
    loading: isFetchingSwapRequests || isFetchingPending || isFetchingAllSwaps,
    error: swapRequestsError || allPendingError || allSwapError,
  };
};
