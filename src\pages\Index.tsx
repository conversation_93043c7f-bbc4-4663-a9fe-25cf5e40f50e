import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Calendar, Users, BarChart3, <PERSON><PERSON><PERSON>, UserCheck } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-gray-900">Sistema Sentinela</h1>
              </div>
            </div>
            <div className="flex gap-4">
              <Link to="/membro-login">
                <Button variant="outline">
                  <UserCheck className="w-4 h-4 mr-2" />
                  Portal do Membro
                </Button>
              </Link>
              <Link to="/login">
                <Button>
                  <Users className="w-4 h-4 mr-2" />
                  Acesso Administrativo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Sistema de Gerenciamento de Escalas
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Gerencie escalas, membros e relatórios de forma eficiente e organizada.
            Acesso para administradores e membros com funcionalidades específicas.
          </p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="w-6 h-6 mr-2 text-blue-600" />
                Gerenciamento de Escalas
              </CardTitle>
              <CardDescription>
                Crie, edite e visualize escalas de serviço
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Sistema completo para organizar escalas de trabalho, com calendário interativo e 
                funcionalidades de edição em tempo real.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-6 h-6 mr-2 text-green-600" />
                Controle de Membros
              </CardTitle>
              <CardDescription>
                Gerencie informações dos membros da equipe
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Cadastro completo de membros com informações pessoais, disponibilidade e 
                histórico de participação nas escalas.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-6 h-6 mr-2 text-purple-600" />
                Relatórios e Dashboard
              </CardTitle>
              <CardDescription>
                Acompanhe estatísticas e gere relatórios
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">
                Dashboard completo com métricas de participação, relatórios detalhados e 
                análises de desempenho da equipe.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Access Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-800">Portal do Membro</CardTitle>
              <CardDescription className="text-blue-600">
                Acesso para membros da equipe
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-blue-700 mb-4">
                <li>• Visualizar suas escalas</li>
                <li>• Gerenciar disponibilidade</li>
                <li>• Solicitar trocas de serviços</li>
                <li>• Atualizar informações pessoais</li>
              </ul>
              <Link to="/membro-login">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  <UserCheck className="w-4 h-4 mr-2" />
                  Acessar Portal do Membro
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="border-gray-200">
            <CardHeader>
              <CardTitle className="text-gray-800">Área Administrativa</CardTitle>
              <CardDescription className="text-gray-600">
                Acesso para administradores
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-gray-700 mb-4">
                <li>• Criar e gerenciar escalas</li>
                <li>• Administrar membros</li>
                <li>• Visualizar relatórios</li>
                <li>• Configurações do sistema</li>
              </ul>
              <Link to="/login">
                <Button variant="outline" className="w-full">
                  <Settings className="w-4 h-4 mr-2" />
                  Acesso Administrativo
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>&copy; 2024 Sistema Sentinela. Desenvolvido para gerenciamento eficiente de escalas.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
