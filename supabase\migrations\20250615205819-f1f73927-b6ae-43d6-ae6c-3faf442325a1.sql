
-- Ad<PERSON><PERSON>r trigger para registrar histórico automaticamente quando status muda para aceita/rejeitada
CREATE OR REPLACE FUNCTION public.registrar_historico_troca()
RETURNS TRIGGER AS $$
BEGIN
    -- Só registra se o status mudou para 'aceita' ou 'rejeitada'
    IF NEW.status IN ('aceita', 'rejeitada') AND OLD.status != NEW.status THEN
        -- Buscar dados da escala origem
        INSERT INTO public.historico_trocas (
            id_solicitacao,
            tipo_troca,
            membro_origem,
            membro_destino,
            escala_origem_data,
            escala_origem_turno,
            observacoes
        )
        SELECT 
            NEW.id,
            CASE 
                WHEN NEW.status = 'aceita' THEN 'aceita'
                ELSE 'rejeitada'
            END,
            NEW.id_solicitante,
            NEW.candidato_selecionado,
            ep.Data,
            ep.Turno,
            CASE 
                WHEN NEW.status = 'aceita' THEN 'Troca aprovada pelo administrador'
                ELSE 'Troca rejeitada pelo administrador'
            END
        FROM public."escalasPassadas" ep
        WHERE ep."ID" = NEW.id_escala_origem;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Criar o trigger
DROP TRIGGER IF EXISTS trigger_historico_troca ON public.solicitacoes_troca;
CREATE TRIGGER trigger_historico_troca
    AFTER UPDATE ON public.solicitacoes_troca
    FOR EACH ROW
    EXECUTE FUNCTION public.registrar_historico_troca();
