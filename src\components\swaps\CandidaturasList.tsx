
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Calendar, MessageSquare, CheckCircle, XCircle } from 'lucide-react';
import { Candidatura } from '@/types/candidaturaTypes';
import { useCandidaturas } from '@/hooks/useCandidaturas';

interface CandidaturasListProps {
  candidaturas: Candidatura[];
  solicitacaoId: string;
  showActions?: boolean;
  onSelectCandidato?: (candidaturaId: string, membroCandidato: string) => void;
}

export const CandidaturasList = ({ 
  candidaturas, 
  solicitacaoId, 
  showActions = false,
  onSelectCandidato 
}: CandidaturasListProps) => {
  const { updateCandidaturaStatusMutation, isUpdatingCandidatura } = useCandidaturas();

  const handleSelectCandidato = (candidatura: Candidatura) => {
    updateCandidaturaStatusMutation.mutate({
      candidaturaId: candidatura.id,
      status: 'selecionada',
    });
    
    if (onSelectCandidato) {
      onSelectCandidato(candidatura.id, candidatura.membro_candidato);
    }
  };

  const handleRejectCandidato = (candidaturaId: string) => {
    updateCandidaturaStatusMutation.mutate({
      candidaturaId,
      status: 'rejeitada',
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ativa':
        return <Badge variant="secondary">Ativa</Badge>;
      case 'selecionada':
        return <Badge variant="default" className="bg-green-500">Selecionada</Badge>;
      case 'rejeitada':
        return <Badge variant="destructive">Rejeitada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  if (candidaturas.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-8">
            <div className="p-4 rounded-full bg-muted/50 w-fit mx-auto mb-4">
              <MessageSquare className="w-8 h-8 text-muted-foreground" />
            </div>
            <p className="text-muted-foreground">Nenhuma candidatura ainda</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="w-5 h-5" />
          Candidatos ({candidaturas.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {candidaturas.map((candidatura) => (
          <div 
            key={candidatura.id} 
            className="flex items-center justify-between p-4 border rounded-lg bg-card hover:shadow-md transition-shadow"
          >
            <div className="flex items-center gap-4">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-primary/10 text-primary">
                  {getInitials(candidatura.membro_candidato)}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <span className="font-semibold">{candidatura.membro_candidato}</span>
                  {getStatusBadge(candidatura.status)}
                </div>
                
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="w-4 h-4" />
                  {new Date(candidatura.data_candidatura).toLocaleDateString('pt-BR')} às{' '}
                  {new Date(candidatura.data_candidatura).toLocaleTimeString('pt-BR', {
                    hour: '2-digit',
                    minute: '2-digit',
                  })}
                </div>
                
                {candidatura.observacoes && (
                  <div className="mt-2 p-2 bg-muted/50 rounded text-sm">
                    <strong>Observações:</strong> {candidatura.observacoes}
                  </div>
                )}
              </div>
            </div>

            {showActions && candidatura.status === 'ativa' && (
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => handleSelectCandidato(candidatura)}
                  disabled={isUpdatingCandidatura}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  Selecionar
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleRejectCandidato(candidatura.id)}
                  disabled={isUpdatingCandidatura}
                  className="flex items-center gap-2"
                >
                  <XCircle className="w-4 h-4" />
                  Rejeitar
                </Button>
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
