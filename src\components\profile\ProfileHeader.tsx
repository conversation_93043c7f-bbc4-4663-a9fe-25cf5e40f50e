
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Camera, Edit3 } from 'lucide-react';

interface ProfileHeaderProps {
  member: any;
  onEditPhoto?: () => void;
  onEditProfile?: () => void;
}

export const ProfileHeader = ({ member, onEditPhoto, onEditProfile }: ProfileHeaderProps) => {
  const getInitials = (name: string) => {
    return name?.split(' ').map(n => n[0]).join('').toUpperCase() || 'M';
  };

  return (
    <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-6 md:p-8 rounded-xl text-white shadow-lg">
      <div className="flex flex-col md:flex-row items-center gap-6">
        <div className="relative group">
          <Avatar className="w-32 h-32 border-4 border-white/20 shadow-xl">
            <AvatarImage src={member.foto_url || member['Link Foto']} />
            <AvatarFallback className="text-3xl bg-white/20">
              {getInitials(member.NomeCompleto || 'Membro')}
            </AvatarFallback>
          </Avatar>
          {onEditPhoto && (
            <Button
              size="sm"
              variant="secondary"
              className="absolute bottom-0 right-0 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={onEditPhoto}
            >
              <Camera className="w-4 h-4" />
            </Button>
          )}
        </div>
        
        <div className="flex-1 text-center md:text-left">
          <div className="flex flex-col md:flex-row md:items-center gap-3 mb-2">
            <h1 className="text-2xl md:text-3xl font-bold">{member.NomeCompleto}</h1>
            {onEditProfile && (
              <Button
                size="sm"
                variant="ghost"
                className="text-white hover:bg-white/10 w-fit mx-auto md:mx-0"
                onClick={onEditProfile}
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Editar
              </Button>
            )}
          </div>
          <p className="text-lg opacity-90 mb-3">{member['Nome Escala']}</p>
          <div className="flex flex-wrap gap-2 justify-center md:justify-start">
            <Badge variant={member.Status === 'ATIVO' ? 'default' : 'secondary'} className="bg-white/20 text-white">
              {member.Status || 'ATIVO'}
            </Badge>
            {member.CargoMinisterio && (
              <Badge variant="outline" className="border-white/30 text-white">
                {member.CargoMinisterio}
              </Badge>
            )}
            {member.Equipe && (
              <Badge variant="outline" className="border-white/30 text-white">
                {member.Equipe}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
