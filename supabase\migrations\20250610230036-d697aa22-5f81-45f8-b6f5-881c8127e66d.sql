
-- C<PERSON>r tabela para candidaturas às trocas
CREATE TABLE public.candidaturas_troca (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  id_solicitacao UUID NOT NULL REFERENCES public.solicitacoes_troca(id) ON DELETE CASCADE,
  membro_candidato TEXT NOT NULL REFERENCES public.membros("Nome Escala"),
  data_candidatura TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  status TEXT NOT NULL DEFAULT 'ativa' CHECK (status IN ('ativa', 'selecionada', 'rejeitada')),
  observacoes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  
  -- Evitar candidaturas duplicadas
  UNIQUE(id_solicitacao, membro_candidato)
);

-- Adicionar campos para suportar o novo fluxo em solicitacoes_troca
ALTER TABLE public.solicitacoes_troca 
ADD COLUMN IF NOT EXISTS candidato_selecionado TEXT REFERENCES public.membros("Nome Escala"),
ADD COLUMN IF NOT EXISTS data_selecao_candidato TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS observacoes_admin TEXT,
ADD COLUMN IF NOT EXISTS responsavel_aprovacao TEXT REFERENCES public.membros("Nome Escala");

-- Atualizar os status possíveis para incluir novos estados
ALTER TABLE public.solicitacoes_troca 
DROP CONSTRAINT IF EXISTS solicitacoes_troca_status_check,
ADD CONSTRAINT solicitacoes_troca_status_check 
CHECK (status IN ('pendente', 'aguardando_candidatos', 'aguardando_aprovacao', 'aceita', 'rejeitada'));

-- Habilitar RLS na nova tabela
ALTER TABLE public.candidaturas_troca ENABLE ROW LEVEL SECURITY;

-- Política para membros visualizarem candidaturas
CREATE POLICY "Membros podem ver candidaturas de suas solicitações ou suas próprias candidaturas" 
ON public.candidaturas_troca 
FOR SELECT 
USING (
  membro_candidato = current_setting('app.current_member', true) OR
  EXISTS (
    SELECT 1 FROM public.solicitacoes_troca st 
    WHERE st.id = candidaturas_troca.id_solicitacao 
    AND st.id_solicitante = current_setting('app.current_member', true)
  )
);

-- Política para membros criarem candidaturas
CREATE POLICY "Membros podem criar suas próprias candidaturas" 
ON public.candidaturas_troca 
FOR INSERT 
WITH CHECK (membro_candidato = current_setting('app.current_member', true));

-- Política para administradores visualizarem todas candidaturas
CREATE POLICY "Administradores podem ver todas candidaturas" 
ON public.candidaturas_troca 
FOR ALL 
USING (true);

-- Política para administradores atualizarem candidaturas
CREATE POLICY "Administradores podem atualizar candidaturas" 
ON public.candidaturas_troca 
FOR UPDATE 
USING (true);

-- Atualizar trigger para updated_at na nova tabela
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_candidaturas_troca_updated_at BEFORE UPDATE ON public.candidaturas_troca 
FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Função para gerenciar estados das solicitações automaticamente
CREATE OR REPLACE FUNCTION manage_swap_request_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Se é uma nova candidatura, mudar status para aguardando_aprovacao se necessário
    IF TG_OP = 'INSERT' THEN
        UPDATE public.solicitacoes_troca 
        SET status = 'aguardando_aprovacao'
        WHERE id = NEW.id_solicitacao 
        AND status = 'aguardando_candidatos';
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER manage_swap_status_on_candidatura 
AFTER INSERT OR UPDATE OR DELETE ON public.candidaturas_troca
FOR EACH ROW EXECUTE FUNCTION manage_swap_request_status();
