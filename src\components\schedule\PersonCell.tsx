
import React from 'react';

interface PersonCellProps {
  personId: string;
  personName: string;
  isFirstPerson: boolean;
  isSelected: boolean;
  isRecentlyAssigned: boolean;
  onClick: () => void;
}

const PersonCell: React.FC<PersonCellProps> = ({
  personId,
  personName,
  isFirstPerson,
  isSelected,
  isRecentlyAssigned,
  onClick,
}) => {
  return (
    <div 
      className={`text-center px-1 
        ${isFirstPerson ? "font-medium" : ""} 
        ${isSelected ? "bg-blue-200" : ""}
        ${isRecentlyAssigned ? "bg-amber-300" : ""}
        hover:bg-gray-100 cursor-pointer mb-1 text-xs border-b border-gray-100`}
      onClick={onClick}
      title={personName}
      style={{ whiteSpace: 'nowrap', overflow: 'visible' }}
    >
      {personName}
    </div>
  );
};

export default PersonCell;
