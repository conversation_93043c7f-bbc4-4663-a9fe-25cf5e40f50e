
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useSchedule } from '@/contexts/ScheduleContext';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Menu } from 'lucide-react';

import SidebarCalendar from '@/components/SidebarCalendar';
import ScheduleForm from '@/components/ScheduleForm';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { useIsMobile } from '@/hooks/use-mobile';
import { format, parse } from 'date-fns';
import { Loading } from '@/components/ui/loading';

const Calendar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { auth } = useAuth();
  const { scheduleState, clearSchedule, toggleDaySelection } = useSchedule();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [calendarHeight, setCalendarHeight] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const isMobile = useIsMobile();
  const navigationAttempted = useRef(false);
  const scheduleLoaded = useRef(false);

  // Check if authenticated, but limit navigation attempts to prevent history API abuse
  useEffect(() => {
    if (!auth.isAuthenticated && !navigationAttempted.current) {
      navigationAttempted.current = true;
      navigate('/login', { replace: true });
    }
  }, [auth.isAuthenticated, navigate]);

  // Load schedule data if coming from edit action
  useEffect(() => {
    if (location.state && location.state.scheduleData && !scheduleLoaded.current) {
      const { scheduleData } = location.state;
      
      try {
        setIsLoading(true);
        // Clear any existing schedule
        clearSchedule();
        
        // Select days from the schedule data
        if (Array.isArray(scheduleData.days) && scheduleData.days.length > 0) {
          console.log("Loading schedule days:", scheduleData.days);
          
          // Process each day to ensure it's a valid Date object
          scheduleData.days.forEach((day: any) => {
            if (!(day instanceof Date)) {
              // Convert to Date if it's not already
              const dateObj = new Date(day);
              if (!isNaN(dateObj.getTime())) {
                toggleDaySelection(dateObj);
              }
            } else {
              toggleDaySelection(day);
            }
          });
          
          // Se há dados de escalas passadas em scheduleData.scheduleData
          if (Array.isArray(scheduleData.scheduleData)) {
            // As informações de pessoas serão processadas pelo ScheduleForm
            console.log("Schedule data loaded with shift assignments:", scheduleData.scheduleData.length);
          }
          
          // Set the flag to prevent reloading
          scheduleLoaded.current = true;
          
          toast.success(`Escala "${scheduleData.title}" carregada para edição`);
        } else {
          toast.error("Dados de dias inválidos na escala");
        }
      } catch (error) {
        console.error("Erro ao carregar escala:", error);
        toast.error("Falha ao carregar a escala para edição");
      } finally {
        setIsLoading(false);
      }
    }
  }, [location.state, clearSchedule, toggleDaySelection]);

  // Handle collapse/expand of sidebar
  const toggleSidebar = useCallback(() => {
    setSidebarCollapsed(prevState => !prevState);
  }, []);

  // Handle generating schedule view with debounce to prevent multiple calls
  const handleGenerateSchedule = useCallback(() => {
    if (scheduleState.selectedDays.length === 0) {
      toast.error('Selecione pelo menos um dia antes de gerar a escala');
      return;
    }

    setIsLoading(true);
    navigate('/schedule-view', { replace: true });
  }, [scheduleState.selectedDays, navigate]);

  // Handle clear selected days
  const handleClearSelection = useCallback(() => {
    clearSchedule();
    scheduleLoaded.current = false; // Reset the loaded flag
    toast.info('Seleção de dias limpa');
  }, [clearSchedule]);

  if (!auth.isAuthenticated) {
    return null;
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        {/* Main App Sidebar */}
        <AppSidebar />
        
        <SidebarInset>
          <div className="flex-1 flex flex-col">
            {/* Header com botão de menu para mobile */}
            <header className="bg-background border-b p-4 flex justify-between items-center md:hidden">
              <div className="flex items-center">
                <SidebarTrigger className="mr-3">
                  <Menu className="h-6 w-6" />
                </SidebarTrigger>
                <h1 className="text-xl font-bold">Criação de Escalas</h1>
              </div>
            </header>

            {/* Calendar Section - Vertical collapse */}
            <SidebarCalendar 
              collapsed={sidebarCollapsed} 
              toggleSidebar={toggleSidebar} 
            />

            {/* Main Content */}
            <div className="flex-1 p-4 md:p-6 overflow-y-auto">
              <header className="mb-6 hidden md:block">
                <div className="flex justify-between items-center">
                  <h1 className="text-2xl font-bold">
                    {location.state?.scheduleData?.title ? 
                      `Editando: ${location.state.scheduleData.title}` : 
                      'Criação de Escalas'}
                  </h1>
                  <div className="space-x-2">
                    <Button 
                      variant="outline" 
                      onClick={handleClearSelection}
                    >
                      Limpar
                    </Button>
                    <Button onClick={handleGenerateSchedule}>
                      Gerar Escala
                    </Button>
                  </div>
                </div>
                <p className="text-muted-foreground mt-1">
                  {location.state?.scheduleData?.title ? 
                    'Edite a escala existente selecionando os dias e preenchendo as informações' : 
                    'Selecione os dias e preencha as informações para criar uma nova escala'}
                </p>
              </header>

              <div className="mt-2">
                <ScheduleForm />
              </div>
              
              {/* Botões de rodapé para mobile */}
              <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4 flex justify-between md:hidden z-10">
                <Button 
                  variant="outline" 
                  onClick={handleClearSelection}
                  className="flex-1 mr-2"
                >
                  Limpar
                </Button>
                <Button 
                  onClick={handleGenerateSchedule}
                  className="flex-1"
                >
                  Gerar Escala
                </Button>
              </div>
            </div>
          </div>
        </SidebarInset>
        
        {isLoading && <Loading />}
      </div>
    </SidebarProvider>
  );
};

export default Calendar;
