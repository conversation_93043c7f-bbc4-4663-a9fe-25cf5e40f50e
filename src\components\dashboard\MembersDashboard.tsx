
import React, { useState, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Users, UserCheck, Calendar, UserX } from 'lucide-react';
import { Member } from '@/types/supabaseTypes';
import { RecentUsers } from './RecentUsers';
import { AdminUpcomingSchedules } from './AdminUpcomingSchedules';
import { QuickSwapAccess } from './QuickSwapAccess';
import { BirthdayList } from './BirthdayList';
import { fetchPendingSwapsCount } from '@/services/api';

export const MembersDashboard = () => {
  const { data: members = [], isLoading } = useQuery({
    queryKey: ['members-dashboard'],
    queryFn: async (): Promise<Member[]> => {
      const { data, error } = await supabase
        .from('membros')
        .select('*')
        .order('NomeCompleto', { ascending: true });
      
      if (error) {
        console.error('Error fetching members:', error);
        throw error;
      }
      
      return data || [];
    }
  });

  const { data: schedules = [] } = useQuery({
    queryKey: ['upcoming-schedules-admin'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('escalasPassadas')
        .select('*')
        .order('Data', { ascending: true });
      
      if (error) {
        console.error('Error fetching schedules:', error);
        return [];
      }
      
      return data || [];
    }
  });

  const { data: pendingSwapsCount = 0 } = useQuery({
    queryKey: ['pending-swaps-count'],
    queryFn: fetchPendingSwapsCount
  });

  // Calcular estatísticas dos membros
  const memberStats = useMemo(() => {
    const activeMembers = members.filter(m => m.Status === 'ATIVO').length;
    const suspendedMembers = members.filter(m => m.Status === 'SUSPENSO').length;
    
    // Aniversariantes do mês atual (formato dd/mm/yyyy)
    const currentMonth = new Date().getMonth() + 1;
    const birthdaysThisMonth = members
      .filter(member => {
        if (!member.DataNascimento) return false;
        
        // Verificar status permitidos (ATIVO, BAIXADO ou SUSPENSO)
        const allowedStatuses = ['ATIVO', 'BAIXADO', 'SUSPENSO'];
        if (!allowedStatuses.includes(member.Status)) return false;
        
        // Dividir a string no formato dd/mm/yyyy
        const parts = member.DataNascimento.split('/');
        if (parts.length !== 3) return false;
        
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10);
        const currentDay = new Date().getDate();
        
        // Mostrar apenas aniversários de hoje em diante no mês atual
        return month === currentMonth && day >= currentDay;
      })
      .map(member => ({
        NomeCompleto: member.NomeCompleto || 'Nome não informado',
        'Nome Escala': member['Nome Escala'] || member.NomeCompleto || 'Membro',
        DataNascimento: member.DataNascimento,
        foto_url: member.foto_url
      }));

    // Usuários recentes (baseado na data de atualização)
    const recentUsers = members
      .filter(m => m.Atualizacao_dataHora)
      .sort((a, b) => {
        const dateA = new Date(a.Atualizacao_dataHora || '');
        const dateB = new Date(b.Atualizacao_dataHora || '');
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 5)
      .map(member => ({
        NomeCompleto: member.NomeCompleto || 'Nome não informado',
        foto_url: member.foto_url,
        Atualizacao_dataHora: member.Atualizacao_dataHora
      }));

    return {
      active: activeMembers,
      suspended: suspendedMembers,
      birthdaysThisMonth,
      recentUsers
    };
  }, [members]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold">Informações do Pessoal</h2>
        <p className="text-muted-foreground">Estatísticas e dados dos membros</p>
      </div>
      
      {/* Cards de Estatísticas */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Usuários Recentes</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memberStats.recentUsers.length}</div>
            <p className="text-xs text-muted-foreground">
              últimas atualizações
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Membros Ativos</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memberStats.active}</div>
            <p className="text-xs text-muted-foreground">
              em atividade
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aniversariantes</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memberStats.birthdaysThisMonth.length}</div>
            <p className="text-xs text-muted-foreground">
              neste mês
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Suspensos</CardTitle>
            <UserX className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{memberStats.suspended}</div>
            <p className="text-xs text-muted-foreground">
              membros suspensos
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Seção de cards divididos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <QuickSwapAccess pendingCount={pendingSwapsCount} />
        <BirthdayList birthdays={memberStats.birthdaysThisMonth} />
      </div>

      {/* Próximas escalas */}
      <AdminUpcomingSchedules schedules={schedules} />

      {/* Usuários recentes */}
      <RecentUsers users={memberStats.recentUsers} />
    </div>
  );
};
