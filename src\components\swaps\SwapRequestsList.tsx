
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, AlertCircle } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';

interface SwapRequestsListProps {
  swapRequests: SwapRequest[] | undefined;
}

export const SwapRequestsList = ({ swapRequests }: SwapRequestsListProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Minhas Solicitações</CardTitle>
        <CardDescription>
          Histórico das suas solicitações de troca
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {swapRequests && swapRequests.length > 0 ? (
            swapRequests.map((request) => (
              <div key={request.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">
                      {new Date(request.data_solicitacao).toLocaleDateString('pt-BR')}
                    </span>
                  </div>
                  <Badge variant={
                    request.status === 'aceita' ? 'default' : 
                    request.status === 'rejeitada' ? 'destructive' : 'secondary'
                  }>
                    {request.status.toUpperCase()}
                  </Badge>
                </div>
                
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Clock className="w-3 h-3" />
                    <span>Tipo: {request.tipo_solicitacao}</span>
                  </div>
                  
                  {request.membro_destino && (
                    <div className="flex items-center gap-2">
                      <User className="w-3 h-3" />
                      <span>Para: {request.membro_destino}</span>
                    </div>
                  )}
                  
                  {request.mensagem && (
                    <p className="mt-2">{request.mensagem}</p>
                  )}
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-4">
              <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Nenhuma solicitação encontrada</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
