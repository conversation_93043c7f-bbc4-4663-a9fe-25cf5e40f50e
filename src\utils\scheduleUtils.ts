
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { DaySchedule, ScheduleData } from '@/types';

// Parse dates from different formats
export const parseDateSafely = (dateStr: string): Date => {
  try {
    // Try different formats
    if (dateStr.includes('-')) {
      return new Date(dateStr); // ISO format (yyyy-MM-dd)
    } else if (dateStr.includes('/')) {
      // DD/MM/YYYY format
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        return new Date(Number(parts[2]), Number(parts[1]) - 1, Number(parts[0]));
      }
    }
    // Default case, just parse as is
    return new Date(dateStr);
  } catch (error) {
    console.error("Error parsing date:", dateStr, error);
    return new Date(); // Return current date as fallback
  }
};

// Generate a meaningful title for a schedule
export const generateScheduleTitle = (selectedDays: Date[]): string => {
  if (!selectedDays || selectedDays.length === 0) {
    return `Escala de ${format(new Date(), 'MMMM', { locale: ptBR })} - Nova`;
  }
  
  // Sort the days
  const sortedDays = [...selectedDays].sort((a, b) => a.getTime() - b.getTime());
  
  // Get first and last day for range
  const firstDay = sortedDays[0];
  const lastDay = sortedDays[sortedDays.length - 1];
  
  // Get month and year
  const month = format(firstDay, 'MMMM', { locale: ptBR });
  const year = format(firstDay, 'yyyy');
  
  // Create week range
  const firstDayOfMonth = format(firstDay, 'd');
  const lastDayOfMonth = format(lastDay, 'd');
  
  return `Escala de ${month} ${year} - ${firstDayOfMonth} a ${lastDayOfMonth}`;
};

// Prepare schedule data for sending to API
export const prepareScheduleData = (
  schedule: Record<string, DaySchedule>,
  selectedDays: Date[],
  persons: any[]
): ScheduleData[] => {
  if (!selectedDays || selectedDays.length === 0 || !schedule || Object.keys(schedule).length === 0) {
    return [];
  }
  
  let id = 1;
  const data: ScheduleData[] = [];
  
  // Sort selected days chronologically
  const sortedDays = [...selectedDays].sort((a, b) => a.getTime() - b.getTime());
  
  sortedDays.forEach(day => {
    const dateKey = format(day, "yyyy-MM-dd");
    const daySchedule = schedule[dateKey];
    
    if (!daySchedule) return;
    
    // Process schedule entries
    // ... implementation details
  });
  
  return data;
};

// Check if a person was scheduled recently based on break days
export const wasPersonScheduledRecently = (
  personId: string, 
  currentDate: Date,
  schedule: Record<string, DaySchedule>,
  breakDays: number
): boolean => {
  const currentTime = currentDate.getTime();
  const breakDaysMs = breakDays * 24 * 60 * 60 * 1000; // Convert break days to milliseconds
  
  // Check all schedules to see if the person was assigned within the break period
  for (const dateKey in schedule) {
    const scheduleDate = schedule[dateKey].date;
    const scheduleTime = scheduleDate.getTime();
    const timeDiff = Math.abs(currentTime - scheduleTime);
    
    // Skip if the date is the same as current date
    if (format(scheduleDate, "yyyy-MM-dd") === format(currentDate, "yyyy-MM-dd")) continue;
    
    // Check if within break period
    if (timeDiff < breakDaysMs) {
      const daySchedule = schedule[dateKey];
      // Check if person is assigned on this day
      for (const shiftKey in daySchedule.shifts) {
        if (daySchedule.shifts[shiftKey as keyof typeof daySchedule.shifts].includes(personId)) {
          return true;
        }
      }
    }
  }
  
  return false;
};
