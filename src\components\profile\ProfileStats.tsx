
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Calendar, RefreshCw, CheckCircle, Clock } from 'lucide-react';

interface ProfileStatsProps {
  upcomingSchedules?: any[];
  swapRequests?: any[];
  minhasCandidaturas?: any[];
}

export const ProfileStats = ({ upcomingSchedules = [], swapRequests = [], minhasCandidaturas = [] }: ProfileStatsProps) => {
  const stats = [
    {
      title: 'Próximas Escalas',
      value: upcomingSchedules.length,
      icon: Calendar,
      color: 'text-blue-600',
      bg: 'bg-blue-50'
    },
    {
      title: 'Solicitações de Troca',
      value: swapRequests.length,
      icon: RefreshCw,
      color: 'text-green-600',
      bg: 'bg-green-50'
    },
    {
      title: 'Candidaturas Ativas',
      value: minhasCandidaturas.filter(c => c.status === 'ativa').length,
      icon: Clock,
      color: 'text-orange-600',
      bg: 'bg-orange-50'
    },
    {
      title: 'Candidaturas Aceitas',
      value: minhasCandidaturas.filter(c => c.status === 'selecionada').length,
      icon: CheckCircle,
      color: 'text-purple-600',
      bg: 'bg-purple-50'
    }
  ];

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
      {stats.map((stat) => (
        <Card key={stat.title} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg ${stat.bg}`}>
                <stat.icon className={`w-5 h-5 ${stat.color}`} />
              </div>
              <div>
                <p className="text-2xl font-bold">{stat.value}</p>
                <p className="text-sm text-muted-foreground">{stat.title}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
