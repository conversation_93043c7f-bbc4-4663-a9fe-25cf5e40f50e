
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { UserPlus } from 'lucide-react';

interface RecentUser {
  NomeCompleto: string;
  foto_url?: string;
  Atualizacao_dataHora?: string;
}

interface RecentUsersProps {
  users: RecentUser[] | undefined;
}

export const RecentUsers = ({ users }: RecentUsersProps) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Usuários Recentes</CardTitle>
        <CardDescription>Membros adicionados recentemente</CardDescription>
      </CardHeader>
      <CardContent>
        {users && users.length > 0 ? (
          <div className="space-y-3">
            {users.slice(0, 5).map((user, index) => (
              <div key={index} className="flex items-center gap-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.foto_url} alt={user.NomeCompleto} />
                  <AvatarFallback>{getInitials(user.NomeCompleto)}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <p className="text-sm font-medium">{user.NomeCompleto}</p>
                  <p className="text-xs text-gray-500">
                    {user.Atualizacao_dataHora || 'Data não informada'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4">
            <UserPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Nenhum usuário recente</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
