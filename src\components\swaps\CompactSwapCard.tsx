
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, MessageSquare } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';

interface CompactSwapCardProps {
  request: SwapRequest;
  showCancelButton?: boolean;
  showCandidateButton?: boolean;
  onCancel?: () => void;
  onCandidate?: () => void;
  isProcessing?: boolean;
}

export const CompactSwapCard = ({
  request,
  showCancelButton = false,
  showCandidateButton = false,
  onCancel,
  onCandidate,
  isProcessing = false
}: CompactSwapCardProps) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pendente':
        return <Badge variant="secondary" className="text-xs">Pendente</Badge>;
      case 'aguardando_candidatos':
        return <Badge variant="outline" className="border-blue-500 text-blue-600 text-xs">A<PERSON><PERSON><PERSON></Badge>;
      case 'aguardando_aprovacao':
        return <Badge variant="outline" className="border-yellow-500 text-yellow-600 text-xs">Aguardando Aprovação</Badge>;
      case 'aceita':
        return <Badge variant="default" className="text-xs">Aceita</Badge>;
      case 'rejeitada':
        return <Badge variant="destructive" className="text-xs">Rejeitada</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const dayName = date.toLocaleDateString('pt-BR', { weekday: 'short' });
    const formattedDate = date.toLocaleDateString('pt-BR', { 
      day: '2-digit', 
      month: '2-digit' 
    });
    return `${dayName}, ${formattedDate}`;
  };

  return (
    <div className="p-3 border rounded-lg bg-card hover:shadow-sm transition-shadow">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-muted-foreground" />
          <span className="font-medium text-sm">
            {formatDate(request.data_solicitacao)}
          </span>
        </div>
        {getStatusBadge(request.status)}
      </div>
      
      {request.mensagem && (
        <div className="mb-3">
          <div className="flex items-start gap-2">
            <MessageSquare className="w-3 h-3 text-muted-foreground mt-0.5" />
            <p className="text-sm text-muted-foreground line-clamp-2">
              {request.mensagem}
            </p>
          </div>
        </div>
      )}

      {(showCancelButton || showCandidateButton) && (
        <div className="flex gap-2 mt-3">
          {showCancelButton && (
            <Button
              size="sm"
              variant="outline"
              onClick={onCancel}
              disabled={isProcessing}
              className="text-xs h-7 flex-1"
            >
              {isProcessing ? 'Cancelando...' : 'Cancelar'}
            </Button>
          )}
          
          {showCandidateButton && (
            <Button
              size="sm"
              onClick={onCandidate}
              disabled={isProcessing}
              className="text-xs h-7 flex-1"
            >
              {isProcessing ? 'Enviando...' : 'Candidatar-se'}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};
