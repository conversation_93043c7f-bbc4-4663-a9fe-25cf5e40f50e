
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useMemberData = (
  memberName: string | undefined | 'admin',
  isEnabled: boolean,
  showAllBirthdays = false
) => {
  // Buscar aniversariantes do mês (apenas futuros)
  const birthdays = useQuery({
    queryKey: ['member-birthdays'],
    queryFn: async () => {
      const now = new Date();
      const currentMonth = now.getMonth() + 1;
      const currentDay = now.getDate();
      
      const { data, error } = await supabase
        .from('membros')
        .select('NomeCompleto, "Nome Escala", DataNascimento')
        .not('DataNascimento', 'is', null)
        .neq('Status', 'DESATIVADO');
      
      if (error) {
        console.error('Error fetching birthdays:', error);
        return [];
      }
      
      return (data || []).filter(member => {
        if (!member.DataNascimento) return false;
        
        const [day, month] = member.DataNascimento.split('/').map(Number);
        const birthMonth = month;
        const birthDay = day;
        
        if (showAllBirthdays) {
          return birthMonth === currentMonth;
        }
        return birthMonth === currentMonth && birthDay >= currentDay;
      });
    },
    enabled: isEnabled
  });

  // Buscar disponibilidade atual - só para membros específicos
  const availability = useQuery({
    queryKey: ['member-availability', memberName],
    queryFn: async () => {
      if (!memberName || memberName === 'admin') return null;
      
      const { data, error } = await supabase
        .from('api_disponibilidade')
        .select('*')
        .eq('Nome', memberName)
        .single();
      
      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching availability:', error);
        return null;
      }
      return data;
    },
    enabled: isEnabled && !!memberName && memberName !== 'admin',
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  });

  return {
    birthdays: birthdays.data,
    availability: availability.data
  };
};
