
import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { AppSidebar } from '@/components/AppSidebar';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { MembersListView } from '@/components/MembersListView';
import { Member } from '@/types/supabaseTypes';

const Members = () => {
  const { auth } = useAuth();

  const { data: members = [], isLoading } = useQuery({
    queryKey: ['members'],
    queryFn: async (): Promise<Member[]> => {
      const { data, error } = await supabase
        .from('membros')
        .select('*')
        .order('NomeCompleto', { ascending: true });
      
      if (error) {
        console.error('Error fetching members:', error);
        throw error;
      }
      
      return data || [];
    },
    enabled: auth.isAuthenticated
  });

  if (!auth.isAuthenticated) {
    return <div>Acesso negado. Faça login para continuar.</div>;
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full">
        <AppSidebar />
        <SidebarInset className="flex-1">
          <div className="flex items-center gap-2 px-4 py-2 border-b lg:hidden">
            <SidebarTrigger />
            <h1 className="text-lg font-semibold">Membros</h1>
          </div>
          <main className="flex-1 overflow-auto">
            <div className="container mx-auto py-6">
              <div className="mb-6 hidden lg:block">
                <h1 className="text-3xl font-bold text-gray-900">Gerenciamento de Membros</h1>
                <p className="text-gray-600">Visualize e gerencie os membros da equipe</p>
              </div>

              {isLoading ? (
                <div className="flex justify-center py-8">
                  <div className="text-gray-500">Carregando membros...</div>
                </div>
              ) : (
                <MembersListView members={members} />
              )}
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default Members;
