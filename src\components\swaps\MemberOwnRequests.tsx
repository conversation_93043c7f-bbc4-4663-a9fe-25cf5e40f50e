
import React, { useEffect, useState, createContext, useContext } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, FileText } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';
import { MemberSwapCard, MemberSwapCardContext } from './MemberSwapCard';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface MemberOwnRequestsProps {
  swapRequests: SwapRequest[] | undefined;
  onRequestUpdate?: () => void;
}

export const MemberOwnRequests = ({ swapRequests, onRequestUpdate }: MemberOwnRequestsProps) => {
  const { auth } = useMemberAuth();
  const memberName = auth.member?.['Nome Escala'];
  const [scheduleDataMap, setScheduleDataMap] = useState<Record<string, any>>({});
  const [candidatesCountMap, setCandidatesCountMap] = useState<Record<string, number>>({});
  const [expandedId, setExpandedId] = useState<string | null>(null);

  // Filtrar apenas as solicitações do próprio membro
  const ownRequests = swapRequests?.filter(request => 
    request.id_solicitante === memberName
  ) || [];

  // Buscar dados das escalas e contagem de candidatos
  useEffect(() => {
    const fetchScheduleDataAndCandidates = async () => {
      if (ownRequests.length === 0) return;

      // Buscar dados das escalas
      const scheduleIds = ownRequests.map(req => req.id_escala_origem).filter(Boolean);
      if (scheduleIds.length > 0) {
        const { data: schedules } = await supabase
          .from('escalasPassadas')
          .select('*')
          .in('ID', scheduleIds);

        if (schedules) {
          const scheduleMap: Record<string, any> = {};
          schedules.forEach(schedule => {
            scheduleMap[schedule.ID] = schedule;
          });
          setScheduleDataMap(scheduleMap);
        }
      }

      // Buscar contagem de candidatos
      const requestIds = ownRequests.map(req => req.id);
      if (requestIds.length > 0) {
        const { data: candidates } = await supabase
          .from('candidaturas_troca')
          .select('id_solicitacao')
          .in('id_solicitacao', requestIds)
          .eq('status', 'ativa');

        if (candidates) {
          const candidatesMap: Record<string, number> = {};
          candidates.forEach(candidate => {
            candidatesMap[candidate.id_solicitacao] = (candidatesMap[candidate.id_solicitacao] || 0) + 1;
          });
          setCandidatesCountMap(candidatesMap);
        }
      }
    };

    fetchScheduleDataAndCandidates();
  }, [ownRequests.length]);

  const handleCancelRequest = async (requestId: string) => {
    if (!memberName) return;

    try {
      await supabase.rpc('set_current_member', { member_name: memberName });

      const { error } = await supabase
        .from('solicitacoes_troca')
        .update({ status: 'cancelada' })
        .eq('id', requestId)
        .eq('id_solicitante', memberName);

      if (error) {
        throw error;
      }

      toast.success('Solicitação cancelada com sucesso!');
      onRequestUpdate?.();
    } catch (error) {
      console.error('Error canceling request:', error);
      toast.error('Erro ao cancelar solicitação');
    }
  };

  if (ownRequests.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Minhas Solicitações
          </CardTitle>
          <CardDescription>
            Suas solicitações de troca de escala
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="p-4 rounded-full bg-muted/50 w-fit mx-auto mb-4">
              <AlertCircle className="w-8 h-8 text-muted-foreground" />
            </div>
            <p className="text-muted-foreground text-lg">Você não possui solicitações de troca</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          Minhas Solicitações ({ownRequests.length})
        </CardTitle>
        <CardDescription>
          Suas solicitações de troca de escala
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4 max-h-[600px] overflow-y-auto">
        {ownRequests.map((request) => (
          <MemberSwapCardContext.Provider
            key={request.id}
            value={{ expandedId, setExpandedId }}
          >
            <MemberSwapCard
              request={{
                ...request,
                scheduleData: scheduleDataMap[request.id_escala_origem],
                id: request.id
              }}
              candidatesCount={candidatesCountMap[request.id] || 0}
              onCancel={() => handleCancelRequest(request.id)}
            />
          </MemberSwapCardContext.Provider>
        ))}
      </CardContent>
    </Card>
  );
};
