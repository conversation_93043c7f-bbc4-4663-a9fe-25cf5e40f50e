
import React from 'react';
import { Report, getNonEmptyFields } from '@/services/api';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface ReportDetailsDialogProps {
  report: Report | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ReportDetailsDialog = ({ report, open, onOpenChange }: ReportDetailsDialogProps) => {
  if (!report) {
    return null;
  }

  const hasIncident = (item: Report): boolean => {
    const alteracao = item["Alteração ?"];
    return alteracao && alteracao.toUpperCase() !== "NÃO. TUDO EM PAZ";
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Detalhes do Relatório</DialogTitle>
          <DialogDescription>
            {report?.data} - {report?.["Tipo Culto"]}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Líder Responsável</h3>
              <p className="text-base">{report["Nome Lider"] || "Não informado"}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Alteração</h3>
              <p className="text-base">{report["Alteração ?"] || "Não informado"}</p>
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Carros</h3>
              <p className="text-base">{report.carros || 0}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Motos</h3>
              <p className="text-base">{report.motos || 0}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Bicicletas</h3>
              <p className="text-base">{report.bikes || 0}</p>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Faltas</h3>
            <p className="text-base">{report.Faltas || "0"}</p>
          </div>

          {/* Filter out empty fields from report */}
          {hasIncident(report) && (
            <div className="pt-2 border-t">
              <h3 className="text-lg font-medium mb-2">Detalhes da Ocorrência</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Campo</TableHead>
                    <TableHead>Valor</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.entries(getNonEmptyFields(report))
                    .filter(([key]) => {
                      // Exclude common fields that are already displayed above
                      const excludedFields = ['id', 'data', 'Nome Lider', 'Tipo Culto', 'carros', 'motos', 'bikes', 'Faltas'];
                      return !excludedFields.includes(key);
                    })
                    .map(([key, value]) => (
                      <TableRow key={key}>
                        <TableCell className="font-medium">{key}</TableCell>
                        <TableCell>{value?.toString() || "-"}</TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ReportDetailsDialog;
