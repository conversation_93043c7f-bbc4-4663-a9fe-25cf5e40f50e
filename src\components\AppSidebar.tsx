
import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader } from '@/components/ui/sidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  LogOut,
  Settings,
  Calendar,
  ListChecks,
  Users,
  RefreshCw,
} from 'lucide-react';

export const AppSidebar = () => {
  const [expanded, setExpanded] = useState(false);
  const [hovered, setHovered] = useState(false);
  const { auth, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  // Function to determine if a sidebar item is active
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .slice(0, 2)
      .join('')
      .toUpperCase();
  };

  return (
    <Sidebar
      className={`bg-sidebar border-r transition-all duration-300 ease-in-out ${
        expanded || hovered ? 'w-64' : 'w-20'
      }`}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => !expanded && setHovered(false)}
    >
      <SidebarHeader className="pb-4 pt-4">
        <Link
          to="/"
          className={`px-4 py-2 font-bold ${
            expanded || hovered ? 'block' : 'hidden'
          }`}
          onClick={() => setExpanded(true)}
        >
          Ministério Sentinela
        </Link>
      </SidebarHeader>
      <SidebarContent>
        <div className={`${isActive('/service-dashboard') ? 'bg-muted' : ''} rounded-md mb-1`}>
          <Link
            to="/dashboard"
            className="flex items-center space-x-2 px-4 py-2"
            onClick={() => setExpanded(true)}
          >
            <LayoutDashboard className="h-4 w-4" />
            <span className={`${expanded || hovered ? 'block' : 'hidden'}`}>Dashboard</span>
          </Link>
        </div>
        <div className={`${isActive('/calendar') ? 'bg-muted' : ''} rounded-md mb-1`}>
          <Link to="/calendar" className="flex items-center space-x-2 px-4 py-2">
            <Calendar className="h-4 w-4" />
            <span className={`${expanded || hovered ? 'block' : 'hidden'}`}>Escalas</span>
          </Link>
        </div>
        <div className={`${isActive('/past-schedules') ? 'bg-muted' : ''} rounded-md mb-1`}>
          <Link to="/past-schedules" className="flex items-center space-x-2 px-4 py-2">
            <ListChecks className="h-4 w-4" />
            <span className={`${expanded || hovered ? 'block' : 'hidden'}`}>Histórico</span>
          </Link>
        </div>
        <div className={`${isActive('/members') || isActive('/membros') ? 'bg-muted' : ''} rounded-md mb-1`}>
          <Link to="/members" className="flex items-center space-x-2 px-4 py-2">
            <Users className="h-4 w-4" />
            <span className={`${expanded || hovered ? 'block' : 'hidden'}`}>Membros</span>
          </Link>
        </div>
        <div className={`${isActive('/admin-swap-requests') || isActive('/trocas') ? 'bg-muted' : ''} rounded-md mb-1`}>
            <Link to="/trocas" className="flex items-center space-x-2 px-4 py-2">
              <RefreshCw className="h-4 w-4" />
              <span className={`${expanded || hovered ? 'block' : 'hidden'}`}>Pedidos de Troca</span>
            </Link>
        </div>
        <div className={`${isActive('/settings') ? 'bg-muted' : ''} rounded-md mb-1`}>
          <Link to="/settings" className="flex items-center space-x-2 px-4 py-2">
            <Settings className="h-4 w-4" />
            <span className={`${expanded || hovered ? 'block' : 'hidden'}`}>Configurações</span>
          </Link>
        </div>
      </SidebarContent>
      <SidebarFooter className="border-t p-4">
        <div className="flex items-center justify-between">
          <div
            className={`flex items-center ${
              expanded || hovered ? 'space-x-2' : 'justify-center'
            }`}
            onClick={() => setExpanded(!expanded)}
            style={{ cursor: 'pointer' }}
          >
            <Avatar className="h-8 w-8">
              <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
              <AvatarFallback>{getInitials(auth.username || '??')}</AvatarFallback>
            </Avatar>
            <span className={`text-sm font-medium ${expanded || hovered ? 'block' : 'hidden'}`}>{auth.username}</span>
          </div>
          <Button variant="outline" size="icon" onClick={handleLogout}>
            <LogOut className="h-4 w-4" />
          </Button>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};
