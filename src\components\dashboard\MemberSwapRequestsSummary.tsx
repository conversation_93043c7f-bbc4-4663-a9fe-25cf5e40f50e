import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { SwapRequest } from '@/types/swapTypes';

interface RequestCount {
  pending: number;
  approved: number;
  rejected: number;
}

interface Props {
  requests: SwapRequest[];
}

export const MemberSwapRequestsSummary = ({ requests }: Props) => {
  const { auth } = useMemberAuth();

  const memberRequests = React.useMemo(() => {
    if (!requests || !auth.member) return [];
    return requests.filter(request =>
      request.id_solicitante === auth.member?.id ||
      request.membro_destino === auth.member?.['Nome Escala']
    );
  }, [requests, auth.member]);

  const requestCounts = React.useMemo(() => {
    const counts: RequestCount = {
      pending: 0,
      approved: 0,
      rejected: 0
    };

    memberRequests.forEach(request => {
      if (request.status === 'pendente') counts.pending++;
      else if (request.status === 'aceita') counts.approved++;
      else if (request.status === 'rejeitada') counts.rejected++;
    });

    return counts;
  }, [memberRequests]);

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center justify-between">
          Minhas Solicitações
          <Link to="/membro-trocas">
            <Button variant="ghost" size="sm">
              <RefreshCw className="w-4 h-4 mr-2" />
              Ver todas
            </Button>
          </Link>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="grid grid-cols-3 gap-2">
          <div className="text-center">
            <Badge variant="secondary" className="w-full justify-center">
              Pendentes
            </Badge>
            <p className="text-2xl font-bold mt-1">{requestCounts.pending}</p>
          </div>
          <div className="text-center">
            <Badge variant="success" className="w-full justify-center">
              Aprovadas
            </Badge>
            <p className="text-2xl font-bold mt-1">{requestCounts.approved}</p>
          </div>
          <div className="text-center">
            <Badge variant="destructive" className="w-full justify-center">
              Recusadas
            </Badge>
            <p className="text-2xl font-bold mt-1">{requestCounts.rejected}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};