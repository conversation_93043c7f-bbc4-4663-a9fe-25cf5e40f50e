
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { date, dayOfWeek, shift } = await req.json()

    console.log('Filtering members for date:', date, 'dayOfWeek:', dayOfWeek, 'shift:', shift)

    // Get all members available for the day of week and shift
    const { data: availableMembers, error: availableError } = await supabaseClient
      .from('api_disponibilidade')
      .select('*')
      .eq(dayOfWeek, 'SIM')

    if (availableError) {
      console.error('Error fetching available members:', availableError)
      throw availableError
    }

    // Get members who are unavailable on the specific date
    const { data: unavailableMembers, error: unavailableError } = await supabaseClient
      .from('disponibilidade_membros')
      .select('membro_nome')
      .eq('data_indisponivel', date)

    if (unavailableError) {
      console.error('Error fetching unavailable members:', unavailableError)
      throw unavailableError
    }

    const unavailableNames = unavailableMembers?.map(m => m.membro_nome) || []

    // Filter out unavailable members
    const filteredMembers = availableMembers?.filter(member => 
      !unavailableNames.includes(member.Nome)
    ) || []

    console.log('Available members after filtering:', filteredMembers.length)

    return new Response(
      JSON.stringify({ 
        members: filteredMembers,
        unavailableCount: unavailableNames.length 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
      }
    )
  }
})
