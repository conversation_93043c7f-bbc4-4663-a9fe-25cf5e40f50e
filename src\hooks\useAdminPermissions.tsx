
import { useMemberAuth } from '@/contexts/MemberAuthContext';

export const useAdminPermissions = () => {
  const { auth } = useMemberAuth();
  
  // Verificar se o usuário tem permissões administrativas
  const hasAdminAccess = () => {
    if (!auth.member || !auth.member.CargoMinisterio) {
      return false;
    }
    
    // Cargos que NÃO têm acesso administrativo
    const memberOnlyRoles = ['Membro', 'MEMBRO'];
    
    // Se o cargo não está na lista de "apenas membros", então tem acesso admin
    const hasAccess = !memberOnlyRoles.includes(auth.member.CargoMinisterio);
    
    console.log('Admin Access Check:', {
      memberName: auth.member.NomeCompleto,
      cargo: auth.member.CargoMinisterio,
      hasAccess
    });
    
    return hasAccess;
  };

  return {
    hasAdminAccess: hasAdminAccess(),
    memberCargo: auth.member?.CargoMinisterio || null
  };
};
