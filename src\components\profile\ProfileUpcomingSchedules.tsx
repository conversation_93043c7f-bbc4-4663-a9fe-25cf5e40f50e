
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, Users } from 'lucide-react';

interface ProfileUpcomingSchedulesProps {
  schedules: any[];
  memberName: string;
}

export const ProfileUpcomingSchedules = ({ schedules, memberName }: ProfileUpcomingSchedulesProps) => {
  const formatDate = (dateStr: string) => {
    try {
      const [day, month, year] = dateStr.split('/');
      const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return date.toLocaleDateString('pt-BR', { 
        day: '2-digit', 
        month: 'short',
        year: 'numeric'
      });
    } catch {
      return dateStr;
    }
  };

  if (schedules.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Próximas Escalas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">Nenhuma escala próxima</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="w-5 h-5" />
          Próximas Escalas ({schedules.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3 max-h-[300px] overflow-y-auto">
        {schedules.slice(0, 5).map((schedule, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-3">
              <div className="text-center">
                <div className="text-sm font-medium">{formatDate(schedule.Data)}</div>
                <div className="text-xs text-muted-foreground">{schedule.Dia}</div>
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-muted-foreground" />
                  <span className="font-medium">{schedule.Turno}</span>
                </div>
                {schedule.Lider && (
                  <div className="flex items-center gap-2 mt-1">
                    <Users className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">Líder: {schedule.Lider}</span>
                  </div>
                )}
              </div>
            </div>
            <Badge variant="outline">Confirmado</Badge>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
