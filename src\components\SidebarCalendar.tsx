
import React, { useState, useEffect, useRef } from 'react';
import { useSchedule } from '@/contexts/ScheduleContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { getPaddedCalendarDays, formatMonth, isToday, isSameDate } from '@/utils/dateUtils';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import CalendarHeader from '@/components/CalendarHeader';
import { ChevronLeft, ChevronRight, MinusCircle, ArrowLeft, ArrowRight, ChevronUp, ChevronDown } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';

interface SidebarCalendarProps {
  collapsed: boolean;
  toggleSidebar: () => void;
}

const SidebarCalendar = ({ collapsed, toggleSidebar }: SidebarCalendarProps) => {
  const { 
    scheduleState, 
    setCurrentMonth, 
    setCurrentYear, 
    toggleDaySelection,
    setServiceLimits,
    setBreakDays
  } = useSchedule();
  
  const [calendarDays, setCalendarDays] = useState<(Date | null)[]>([]);
  const selectedDaysScrollRef = useRef<HTMLDivElement>(null);
  const [calendarCollapsed, setCalendarCollapsed] = useState(false);
  
  // Update the calendar days when year/month changes
  useEffect(() => {
    const days = getPaddedCalendarDays(scheduleState.year, scheduleState.month);
    setCalendarDays(days);
  }, [scheduleState.year, scheduleState.month]);
  
  const handlePreviousMonth = () => {
    if (scheduleState.month === 0) {
      setCurrentYear(scheduleState.year - 1);
      setCurrentMonth(11);
    } else {
      setCurrentMonth(scheduleState.month - 1);
    }
  };
  
  const handleNextMonth = () => {
    if (scheduleState.month === 11) {
      setCurrentYear(scheduleState.year + 1);
      setCurrentMonth(0);
    } else {
      setCurrentMonth(scheduleState.month + 1);
    }
  };
  
  const handleDayClick = (day: Date | null) => {
    if (day) {
      toggleDaySelection(day);
    }
  };
  
  const handleServiceLimitsChange = (value: string) => {
    setServiceLimits(Number(value));
    toast.info(`Limite de serviços por pessoa definido para ${value}`);
  };
  
  const handleBreakDaysChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(event.target.value);
    if (!isNaN(value) && value >= 0) {
      setBreakDays(value);
      toast.info(`Folga mínima entre serviços definida para ${value} dias`);
    }
  };
  
  const isDateSelected = (date: Date | null): boolean => {
    if (!date) return false;
    return scheduleState.selectedDays.some(selectedDate => isSameDate(selectedDate, date));
  };
  
  const scrollLeft = () => {
    if (selectedDaysScrollRef.current) {
      selectedDaysScrollRef.current.scrollBy({ left: -100, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (selectedDaysScrollRef.current) {
      selectedDaysScrollRef.current.scrollBy({ left: 100, behavior: 'smooth' });
    }
  };
  
  const toggleCalendarCollapse = () => {
    setCalendarCollapsed(!calendarCollapsed);
  };
  
  if (collapsed) {
    return (
      <div className="w-[60px] h-full flex flex-col py-4 border-r bg-background">
        <div className="flex justify-center mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="rounded-full"
          >
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>
        <div className="flex flex-col items-center space-y-8">
          <div className="rotate-90 whitespace-nowrap font-medium text-sm">
            {format(new Date(scheduleState.year, scheduleState.month, 1), "MMMM yyyy", { locale: ptBR })}
          </div>
          <div className="flex flex-col space-y-1">
            <Button variant="ghost" size="icon" onClick={handlePreviousMonth}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={handleNextMonth}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`transition-all duration-300 ${calendarCollapsed ? 'h-16' : ''} overflow-hidden border-b bg-background`}>
      <div className="flex justify-between items-center px-4 h-16">
        <h2 className="text-lg font-semibold">Escala Mensal</h2>
        <Button
          variant="ghost"
          size="icon"
          onClick={toggleCalendarCollapse}
          className="rounded-full"
        >
          {calendarCollapsed ? <ChevronDown className="h-5 w-5" /> : <ChevronUp className="h-5 w-5" />}
        </Button>
      </div>
      
      {!calendarCollapsed && (
        <div className="px-4 grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Configuration Card */}
          <Card className="mb-4">
            <CardHeader className="pb-2">
              <CardTitle className="text-md">CONFIGURAÇÃO SERVIÇO</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Limite Mês</Label>
                <Select 
                  value={scheduleState.serviceLimits.toString()}
                  onValueChange={handleServiceLimitsChange}
                >
                  <SelectTrigger className="w-[70px] h-8">
                    <SelectValue placeholder="Limite" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">01</SelectItem>
                    <SelectItem value="2">02</SelectItem>
                    <SelectItem value="3">03</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Folga Serviços</Label>
                <Input 
                  type="number" 
                  className="w-[70px] h-8 text-center" 
                  value={scheduleState.breakDays} 
                  onChange={handleBreakDaysChange}
                  min={0}
                  defaultValue="21"
                />
              </div>
              
              <div className="space-y-2 text-sm">
                <h3 className="font-medium">Legenda:</h3>
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 rounded-sm bg-green-100 border border-green-500"></div>
                  <span className="text-xs">Dia selecionado</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="h-4 w-4 rounded-sm bg-blue-50 border border-gray-300"></div>
                  <span className="text-xs">Hoje</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Calendar Card - Takes 2/3 of the space */}
          <Card className="mb-4 col-span-2">
            <CardHeader className="pb-2">
              <div className="flex items-center justify-center w-full space-x-4 mb-2">
                <Button variant="ghost" size="icon" onClick={handlePreviousMonth}>
                  <ChevronLeft className="h-5 w-5" />
                </Button>
                <h2 className="text-lg font-semibold text-center uppercase">
                  {format(new Date(scheduleState.year, scheduleState.month, 1), "MMMM yyyy", { locale: ptBR })}
                </h2>
                <Button variant="ghost" size="icon" onClick={handleNextMonth}>
                  <ChevronRight className="h-5 w-5" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Calendar Header */}
              <div className="grid grid-cols-7 gap-1 text-center mb-1">
                {['DOM', 'SEG', 'TER', 'QUA', 'QUI', 'SEX', 'SÁB'].map((day, index) => (
                  <div key={index} className="text-xs font-medium text-muted-foreground">
                    {day}
                  </div>
                ))}
              </div>
              
              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-1 border rounded-md overflow-hidden">
                {calendarDays.map((day, index) => (
                  <div 
                    key={index}
                    className={`min-h-[40px] p-1 border flex items-center justify-center 
                      ${!day ? 'bg-gray-50 cursor-default' : 'cursor-pointer hover:bg-gray-100'} 
                      ${day && isToday(day) ? 'bg-blue-50' : ''}
                      ${day && isDateSelected(day) ? 'bg-green-100 border-green-500 border-2' : ''}`}
                    onClick={() => handleDayClick(day)}
                  >
                    {day && (
                      <span className="text-sm font-medium">
                        {format(day, 'd')}
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Selected Days with horizontal scroll and navigation buttons - Now with improved carousel design */}
      {scheduleState.selectedDays.length > 0 && (
        <div className="px-4 py-2 border-t border-b relative">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium">Dias selecionados:</h3>
          </div>
          
          <div className="relative w-full flex items-center max-w-full">
            {/* Left arrow (always visible) */}
            <Button 
              variant="ghost" 
              size="icon"
              onClick={scrollLeft}
              className="h-8 w-8 flex-shrink-0 bg-white/80 shadow-sm rounded-full z-10 absolute left-0"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            {/* Scrollable area */}
            <div className="overflow-hidden w-full px-10">
              <div 
                ref={selectedDaysScrollRef}
                className="flex gap-1 overflow-x-auto scrollbar-none px-2 py-1 max-w-full"
                style={{ scrollBehavior: 'smooth', msOverflowStyle: 'none', scrollbarWidth: 'none' }}
              >
                {scheduleState.selectedDays
                  .sort((a, b) => a.getTime() - b.getTime())
                  .map((day) => (
                    <Button 
                      key={day.toString()} 
                      variant="outline" 
                      size="sm"
                      className="h-8 px-2 py-1 text-xs shrink-0 whitespace-nowrap flex-shrink-0"
                      onClick={() => toggleDaySelection(day)}
                    >
                      {format(day, 'dd/MM')}
                      <MinusCircle className="ml-1 h-3 w-3 text-red-500" />
                    </Button>
                  ))}
              </div>
            </div>
            
            {/* Right arrow (always visible) */}
            <Button 
              variant="ghost" 
              size="icon"
              onClick={scrollRight}
              className="h-8 w-8 flex-shrink-0 bg-white/80 shadow-sm rounded-full z-10 absolute right-0"
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SidebarCalendar;
