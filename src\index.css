
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 262.1 83.3% 57.8%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;

    --sidebar-background: 210 40% 98%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 221.2 83.2% 53.3%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 221.2 83.2% 53.3%;
    
    /* Custom semantic colors */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 210 40% 98%;
    --warning: 38.9 92.3% 50.8%;
    --warning-foreground: 222.2 84% 4.9%;
    --info: 199.3 93.7% 42.2%;
    --info-foreground: 210 40% 98%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 263.4 92.5% 64.3%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
    
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 217.2 91.2% 59.8%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Custom semantic colors */
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 222.2 84% 4.9%;
    --warning: 43.8 91.4% 53.9%;
    --warning-foreground: 222.2 84% 4.9%;
    --info: 198.8 89.1% 52.9%;
    --info-foreground: 222.2 84% 4.9%;
  }

  /* Prevent zoom on double tap in mobile - ESSENTIAL MOBILE FIX */
  * {
    touch-action: manipulation;
  }

  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

.scrollbar-none {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

/* Modern card styles for dark mode */
.dark .card {
  background: linear-gradient(to bottom right, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.8));
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
}

/* Glass effect for dark mode UI elements */
.dark .glass-morphism {
  background: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

/* IMPROVED Mobile menu - CRITICAL FIX */
@media (max-width: 768px) {
  .mobile-menu-trigger {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 48px !important;
    height: 48px !important;
    background: white;
    border: 2px solid #ff6b35;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
    z-index: 60;
    position: fixed;
    top: 16px;
    left: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .mobile-menu-trigger:hover {
    background: #fff1ed;
    border-color: #e55a2b;
    transform: scale(1.05);
  }

  .mobile-menu-trigger:active {
    transform: scale(0.95);
  }

  .mobile-menu-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 50;
    backdrop-filter: blur(8px);
  }

  .mobile-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: 320px;
    background: white;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    z-index: 60;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-sidebar.open {
    transform: translateX(0);
  }

  /* Ensure hamburger menu is always visible and functional */
  .sidebar-trigger {
    display: block !important;
    position: fixed !important;
    top: 16px !important;
    left: 16px !important;
    z-index: 70 !important;
    width: 48px !important;
    height: 48px !important;
    background: white !important;
    border: 2px solid #ff6b35 !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2) !important;
  }
}

.carousel-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.carousel-content {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none;
}

.carousel-content::-webkit-scrollbar {
  display: none;
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  cursor: pointer;
}

.carousel-arrow-left {
  left: 0.5rem;
}

.carousel-arrow-right {
  right: 0.5rem;
}

/* Forced horizontal layout - override all other styles */
.schedule-view-container {
  width: 100% !important;
  padding: 1rem !important;
  overflow-x: auto !important;
  -webkit-overflow-scrolling: touch !important;
  min-width: 1200px !important;
  display: block !important;
}

/* Absolute horizontal grid - no wrapping */
.schedule-grid {
  display: flex !important;
  flex-wrap: nowrap !important;
  gap: 1rem !important;
  width: max-content !important;
  min-width: 100% !important;
}

/* Fixed day columns - no shrinking */
.schedule-day-column {
  width: 160px !important;
  min-width: 160px !important;
  flex-shrink: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 0.75rem !important;
  border: 1px solid var(--border) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem !important;
  background: var(--secondary) !important;
}

/* Day column styling */
.schedule-day-column {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 160px;
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  padding: 0.75rem;
  background: var(--secondary);
}

.schedule-day-header {
  font-weight: bold;
  text-align: center;
  padding: 0.5rem;
  background: var(--primary);
  color: var(--primary-foreground);
  border-radius: 0.25rem;
  margin-bottom: 0.5rem;
}

/* Turns container - always vertical */
.schedule-turns-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* Turn types in specific order */
.turn-manha { order: 1; background: #f0f9ff; }
.turn-tarde { order: 2; background: #fff7ed; }
.turn-noite { order: 3; background: #fef2f2; }
.turn-ebd { order: 4; background: #f0fdf4; }
.turn-culto1 { order: 5; background: #f5f3ff; }
.turn-culto2 { order: 6; background: #ecfdf5; }

.schedule-turn-item {
  border-radius: 0.25rem;
  padding: 0.75rem;
  font-size: 0.85rem;
}

.turn-header {
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: var(--primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.turn-header::before {
  content: "";
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: currentColor;
}

.turn-members {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding-left: 1.25rem;
}

/* Generate image button */
.generate-image-btn {
  width: 200px;
  padding: 0.75rem;
  margin: 2rem auto 0;
  background: var(--primary);
  color: var(--primary-foreground);
  border-radius: 0.5rem;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: transform 0.2s;
}

.generate-image-btn:hover {
  transform: scale(1.02);
}

/* Orange theme colors for company branding - ENHANCED */
.orange-primary {
  background-color: #ff6b35;
  color: white;
}

.orange-primary:hover {
  background-color: #e55a2b;
}

.orange-secondary {
  background-color: #fff1ed;
  color: #c53030;
  border: 1px solid #feb2a8;
}

.orange-accent {
  background-color: #2d3339;
  color: white;
}

.orange-gradient {
  background: linear-gradient(135deg, #ff6b35 0%, #e55a2b 100%);
}

/* Print-specific styles */
@media print {
  body * {
    visibility: hidden;
  }
  
  .print-container,
  .print-container * {
    visibility: visible;
  }
  
  .print-container {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    margin: 0;
    padding: 0;
  }
  
  /* Schedule table print styles */
  .schedule-table {
    width: 100% !important;
    border-collapse: collapse;
  }
  
  .schedule-table th,
  .schedule-table td {
    border: 1px solid #ddd;
    padding: 8px;
    background-color: white !important;
    color: black !important;
  }
  
  /* Hide non-essential elements */
  .no-print,
  .sidebar,
  .header,
  button {
    display: none !important;
  }
  
  /* Force background colors in print */
  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
  
  /* Improve text readability */
  * {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  
  /* Page breaks */
  .page-break {
    page-break-after: always;
  }
}
