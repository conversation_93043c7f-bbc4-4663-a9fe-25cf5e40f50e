
import React, { useState, useMemo, createContext, useContext } from 'react';
import { Link } from 'react-router-dom';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { MemberLayout } from '@/components/MemberLayout';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { RefreshCw, Calendar } from 'lucide-react';
import { toast } from 'sonner';
import { ScheduleFilters } from '@/components/schedule/ScheduleFilters';

// Contexto para gerenciar estado expandido
const MemberScheduleCardContext = createContext<{
  expandedId: string | null;
  setExpandedId: (id: string | null) => void;
}>({
  expandedId: null,
  setExpandedId: () => {},
});

const MemberSchedules = () => {
  const { auth } = useMemberAuth();
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    dateFrom: '',
    dateTo: '',
    shift: 'all',
    status: 'all'
  });

  // Buscar escalas do membro
  const { data: schedules, refetch } = useQuery({
    queryKey: ['member-schedules', auth.member?.['Nome Escala']],
    queryFn: async () => {
      if (!auth.member?.['Nome Escala']) return [];
      
      await supabase.rpc('set_current_member', { member_name: auth.member['Nome Escala'] });
      
      console.log('Fetching schedules for member:', auth.member['Nome Escala']);
      
      const { data, error } = await supabase
        .from('escalasPassadas')
        .select('*')
        .or(`Membro1.eq.${auth.member['Nome Escala']},Membro2.eq.${auth.member['Nome Escala']},Membro3.eq.${auth.member['Nome Escala']},Membro4.eq.${auth.member['Nome Escala']},Lider.eq.${auth.member['Nome Escala']}`)
        ;
      
      if (error) {
        console.error('Error fetching schedules:', error);
        return [];
      }
      
      console.log('All schedules found:', data?.length || 0);
      return data || [];
    },
    enabled: !!auth.member,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
  });

  // Filtrar escalas baseado nos filtros aplicados
  const filteredSchedules = useMemo(() => {
    if (!schedules) return [];

    return schedules.filter(schedule => {
      // Filtro por data inicial
      if (filters.dateFrom && schedule.Data) {
        const dateParts = schedule.Data.split('/');
        if (dateParts.length === 3) {
          const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
          const filterDate = new Date(filters.dateFrom);
          if (scheduleDate < filterDate) return false;
        }
      }

      // Filtro por data final
      if (filters.dateTo && schedule.Data) {
        const dateParts = schedule.Data.split('/');
        if (dateParts.length === 3) {
          const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
          const filterDate = new Date(filters.dateTo);
          if (scheduleDate > filterDate) return false;
        }
      }

      // Filtro por turno
      if (filters.shift !== 'all' && schedule.Turno !== filters.shift) {
        return false;
      }

      // Filtro por status (assumindo que existe um campo status, senão usar um padrão)
      if (filters.status !== 'all') {
        const scheduleStatus = schedule['STATUS ALERTA'] || 'confirmado'; // padrão
        if (scheduleStatus !== filters.status) return false;
      }

      return true;
    }).sort((a, b) => {
      if (!a.Data || !b.Data) return 0;
      const partsA = a.Data.split('/');
      const partsB = b.Data.split('/');
      if (partsA.length !== 3 || partsB.length !== 3) return 0;

      // new Date(year, monthIndex, day)
      const dateA = new Date(parseInt(partsA[2]), parseInt(partsA[1]) - 1, parseInt(partsA[0]));
      const dateB = new Date(parseInt(partsB[2]), parseInt(partsB[1]) - 1, parseInt(partsB[0]));

      if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) return 0;

      // Ordena da data mais recente para a mais antiga
      return dateB.getTime() - dateA.getTime();
    });
  }, [schedules, filters]);


  const getMemberRole = (schedule: any) => {
    const memberName = auth.member?.['Nome Escala'];
    if (schedule.Lider === memberName) return 'Líder';
    if (schedule.Membro1 === memberName) return 'Membro 1';
    if (schedule.Membro2 === memberName) return 'Membro 2';
    if (schedule.Membro3 === memberName) return 'Membro 3';
    if (schedule.Membro4 === memberName) return 'Membro 4';
    return 'Membro';
  };

  const clearFilters = () => {
    setFilters({
      dateFrom: '',
      dateTo: '',
      shift: 'all',
      status: 'all'
    });
  };

  const isFutureSchedule = (schedule: any) => {
    if (!schedule.Data) return false;
    const dateParts = schedule.Data.split('/');
    if (dateParts.length !== 3) return false;
    const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
    return scheduleDate >= new Date();
  };

  return (
    <MemberScheduleCardContext.Provider value={{ expandedId, setExpandedId }}>
      <MemberLayout>
        <div className="space-y-6 p-4 md:p-6">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Minhas Escalas</h1>
          <p className="text-gray-600 mt-2">
            Visualize todas as suas escalas e solicite trocas quando necessário
          </p>
        </div>

        <ScheduleFilters
          filters={filters}
          onFiltersChange={setFilters}
          onClearFilters={clearFilters}
        />

        <div className="space-y-4">
          {filteredSchedules && filteredSchedules.length > 0 ? (
            filteredSchedules.map((schedule) => (
              <Card key={schedule.ID} className="overflow-hidden">
                <CardHeader
                  className="cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => setExpandedId(expandedId === schedule.ID ? null : schedule.ID)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {expandedId === schedule.ID ? (
                        <ChevronUp className="w-4 h-4" />
                      ) : (
                        <ChevronDown className="w-4 h-4" />
                      )}
                      <div>
                        <CardTitle className="flex items-center gap-2">
                          <Calendar className="w-5 h-5" />
                          {schedule.Data} - {schedule.Turno}
                          {isFutureSchedule(schedule) && (
                            <Badge variant="default" className="ml-2">
                              Futura
                            </Badge>
                          )}
                        </CardTitle>
                        <CardDescription>
                          {schedule.Dia}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Badge variant="outline">
                        {getMemberRole(schedule)}
                      </Badge>
                      {isFutureSchedule(schedule) && (
                        <Link to={`/membro-trocas?scheduleId=${schedule.ID}`}>
                          <Button size="sm">
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Solicitar Troca
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className={expandedId === schedule.ID ? 'block' : 'hidden'}>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Líder</p>
                      <p className="text-sm">{schedule.Lider || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Membro 1</p>
                      <p className="text-sm">{schedule.Membro1 || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Membro 2</p>
                      <p className="text-sm">{schedule.Membro2 || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Membro 3</p>
                      <p className="text-sm">{schedule.Membro3 || 'N/A'}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-600">Membro 4</p>
                      <p className="text-sm">{schedule.Membro4 || 'N/A'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">
                  {schedules?.length === 0 
                    ? 'Nenhuma escala encontrada' 
                    : 'Nenhuma escala corresponde aos filtros aplicados'
                  }
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
      </MemberLayout>
    </MemberScheduleCardContext.Provider>
  );
};

export default MemberSchedules;
