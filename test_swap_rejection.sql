-- Test script to verify swap rejection/cancellation works
-- Run this AFTER applying the fix_swap_rejection_issue.sql

-- Test 1: Check if RLS policies exist
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'solicitacoes_troca';

-- Test 2: Check if historico_trocas.membro_destino allows NULL
SELECT column_name, is_nullable, data_type 
FROM information_schema.columns 
WHERE table_name = 'historico_trocas' 
AND column_name = 'membro_destino';

-- Test 3: Verify the trigger function exists and is updated
SELECT routine_name, routine_definition 
FROM information_schema.routines 
WHERE routine_name = 'registrar_historico_troca';

-- Test 4: Check if the trigger is properly attached
SELECT trigger_name, event_manipulation, action_timing, action_statement
FROM information_schema.triggers 
WHERE trigger_name = 'trigger_historico_troca';

-- Test 5: Verify status constraint includes all required statuses
SELECT constraint_name, check_clause
FROM information_schema.check_constraints
WHERE constraint_name = 'solicitacoes_troca_status_check';
