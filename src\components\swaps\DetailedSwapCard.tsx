
import React, { useState, useContext } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, Users, X, MapPin, AlertTriangle } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';

interface DetailedSwapCardContextType {
  expandedId: string | null;
  setExpandedId: (id: string | null) => void;
}

export const DetailedSwapCardContext = React.createContext<DetailedSwapCardContextType>({
  expandedId: null,
  setExpandedId: () => {},
});

interface DetailedSwapCardProps {
  request: SwapRequest;
  scheduleData?: any;
  candidatesCount?: number;
  showCancelButton?: boolean;
  showCandidateButton?: boolean;
  onCancel?: () => void;
  onCandidate?: () => void;
  isProcessing?: boolean;
  disabled?: boolean;
}

const formatDateBR = (dateString: string) => {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return 'Data inválida';
    }
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch {
    return 'Data inválida';
  }
};

export const DetailedSwapCard = ({
  request,
  scheduleData,
  candidatesCount = 0,
  showCancelButton = false,
  showCandidateButton = false,
  onCancel,
  onCandidate,
  isProcessing = false,
  disabled = false,
}: DetailedSwapCardProps) => {
  const { expandedId, setExpandedId } = useContext(DetailedSwapCardContext);
  const isOpen = expandedId === request.id;
  
  const handleToggle = () => {
    console.log('Toggling card', request.id, 'current expandedId:', expandedId);
    setExpandedId(isOpen ? null : request.id);
  };
  const getStatusBadge = (status: string) => {
    if (disabled && status === 'aguardando_candidatos') {
      return <Badge variant="outline" className="border-orange-500 text-orange-600">Pendente</Badge>;
    }
    switch (status) {
      case 'aguardando_candidatos':
        return <Badge variant="outline" className="border-yellow-500 text-yellow-600">Aguardando Voluntários</Badge>;
      case 'aguardando_aprovacao':
        return <Badge variant="outline" className="border-blue-500 text-blue-600">Aguardando Aprovação</Badge>;
      case 'aceita':
        return <Badge variant="default">Aprovada</Badge>;
      case 'rejeitada':
        return <Badge variant="destructive">Rejeitada</Badge>;
      case 'cancelada':
        return <Badge variant="outline" className="border-gray-500 text-gray-600">Cancelada</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <Card className={`border-border ${disabled ? 'opacity-60' : ''} cursor-pointer`} onClick={() => handleToggle()}>
      <CardContent className="p-4">
        {/* Seção Resumida */}
        <div className="flex justify-between items-center gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 truncate">
              <Calendar className="w-4 h-4 text-primary flex-shrink-0" />
              <span className="truncate">
                {(scheduleData?.Data)} • {scheduleData?.Turno}
                <span className="text-muted-foreground ml-1">({scheduleData?.Dia})</span>
              </span>
            </div>
            
            {!isOpen && request.mensagem && (
              <div className="text-sm text-muted-foreground truncate mt-1">
                {request.mensagem}
              </div>
            )}
          </div>
          
                   
          <div className="ml-2 flex-shrink-0">
            {getStatusBadge(request.status)}
            {disabled && (
              <Badge variant="outline" className="ml-2 border-orange-500 text-orange-600">
                Já Candidatado
              </Badge>
            )}
          </div>
          <div className={`transition-transform ${isOpen ? 'rotate-180' : ''} flex-shrink-0`}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {/* Seção Detalhada */}
        {isOpen && (
        <Card className="border-border mt-4">
          <CardContent className="p-4 space-y-2" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-primary" />
              <span className="font-medium">Solicitante:</span>
              <span>{request.id_solicitante.split(' ')[0]}</span>
            </div>

            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-primary" />
              <span className="font-medium">Data solicitação:</span>
              <span>{formatDateBR(request.data_solicitacao)}</span>
            </div>

            {candidatesCount > 0 && (
              <div className="flex items-center gap-2">
                <Users className="w-4 h-4 text-primary" />
                <span className="font-medium">Candidaturas:</span>
                <span>{candidatesCount}</span>
              </div>
            )}

            {request.mensagem && (
              <div>
                <div className="font-medium">Motivo:</div>
                <div className="text-sm text-muted-foreground">
                  {request.mensagem}
                </div>
              </div>
            )}

            {/* Ações */}
            <div className="flex gap-3 pt-2">
              {showCancelButton && (
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={onCancel}
                  disabled={isProcessing}
                  className="flex items-center gap-2"
                >
                  <X className="w-4 h-4" />
                  {isProcessing ? 'Cancelando...' : 'Cancelar'}
                </Button>
              )}

              {showCandidateButton && (
                <Button
                  size="sm"
                  onClick={onCandidate}
                  disabled={isProcessing || disabled}
                  className="flex items-center gap-2"
                >
                  <User className="w-4 h-4" />
                  {isProcessing ? 'Enviando...' : disabled ? 'Já Candidatado' : 'Me Candidatar'}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
        )}
      </CardContent>
    </Card>
  );
};

