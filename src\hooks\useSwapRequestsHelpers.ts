
import { supabase } from '@/integrations/supabase/client';

export const useSwapRequestsHelpers = () => {
  // Buscar candidatos para cada solicitação
  const getCandidatesForRequest = async (requestId: string): Promise<string[]> => {
    const { data, error } = await supabase
      .from('candidaturas_troca')
      .select('membro_candidato')
      .eq('id_solicitacao', requestId);

    if (error) {
      console.error('Error fetching candidates:', error);
      return [];
    }

    return data?.map(c => c.membro_candidato) || [];
  };

  return {
    getCandidatesForRequest,
  };
};
