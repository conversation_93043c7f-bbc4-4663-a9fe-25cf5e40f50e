
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { 
  <PERSON><PERSON><PERSON>, 
  Bar, 
  XAxis, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ResponsiveContainer,
  CartesianGrid
} from 'recharts';
import { AspectRatio } from '@/components/ui/aspect-ratio';

interface ChartData {
  week: string;
  Carros: number;
  Motos: number;
  Bicicletas: number;
  dateRange: string;
}

interface VehicleComparisonChartProps {
  data: ChartData[];
}

const VehicleComparisonChart = ({ data }: VehicleComparisonChartProps) => {
  return (
    <Card className="col-span-full md:col-span-2 lg:col-span-4">
      <CardHeader>
        <CardTitle>Comparativo de Veículos por Semana</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Reduced height by 50% using a lower aspect ratio */}
        <AspectRatio ratio={42/9} className="bg-background rounded-md">
          <ChartContainer
            config={{
              Carros: {
                color: "#4f46e5",
              },
              Motos: {
                color: "#8b5cf6",
              },
              Bicicletas: {
                color: "#a78bfa",
              },
            }}
            className="w-full h-full"
          >
            <BarChart
              data={data}
              margin={{
                top: 20,
                right: 30,
                left: 0,
                bottom: 30,
              }}
              barSize={20}
              barGap={8}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
              <XAxis
                dataKey="week"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12 }}
              />
              <ChartTooltip
                content={
                  <ChartTooltipContent nameKey="name" labelKey="dateRange" />
                }
              />
              <Legend />
              <Bar dataKey="Carros" fill="var(--color-Carros)" radius={[4, 4, 0, 0]} />
              <Bar dataKey="Motos" fill="var(--color-Motos)" radius={[4, 4, 0, 0]} />
              <Bar dataKey="Bicicletas" fill="var(--color-Bicicletas)" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ChartContainer>
        </AspectRatio>
      </CardContent>
    </Card>
  );
};

export default VehicleComparisonChart;
