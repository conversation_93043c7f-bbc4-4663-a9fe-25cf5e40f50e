-- Fix the status constraint to allow 'cancelada' status
-- Run this in Supabase SQL Editor

-- Update the check constraint to include 'cancelada' status
ALTER TABLE public.solicitacoes_troca 
DROP CONSTRAINT IF EXISTS solicitacoes_troca_status_check,
ADD CONSTRAINT solicitacoes_troca_status_check 
CHECK (status IN ('pendente', 'aguardando_candidatos', 'aguardando_aprovacao', 'aceita', 'rejeitada', 'cancelada'));

-- Verify the constraint was updated
SELECT constraint_name, check_clause
FROM information_schema.check_constraints
WHERE constraint_name = 'solicitacoes_troca_status_check';
