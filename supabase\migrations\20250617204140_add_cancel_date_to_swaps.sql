-- Adicionar coluna data_cancelamento para registrar quando solicitações são canceladas
ALTER TABLE public.solicitacoes_troca 
ADD COLUMN IF NOT EXISTS data_cancelamento TIMESTAMP WITH TIME ZONE;

-- Atualizar função de histórico para registrar cancelamentos
CREATE OR REPLACE FUNCTION public.registrar_historico_troca()
RETURNS TRIGGER AS $$
BEGIN
    -- Registrar histórico para status aceita, rejeitada ou cancelada
    IF NEW.status IN ('aceita', 'rejeitada', 'cancelada') AND (OLD.status IS NULL OR OLD.status != NEW.status) THEN
        INSERT INTO public.historico_trocas (
            id_solicitacao,
            tipo_troca,
            membro_origem,
            membro_destino,
            escala_origem_data,
            escala_origem_turno,
            observacoes
        )
        SELECT 
            NEW.id,
            NEW.status,
            NEW.id_solicitante,
            COALESCE(NEW.candidato_selecionado, NEW.membro_destino),
            COALESCE(ep."Data", 'Data não encontrada'),
            COALESCE(ep."Turno", 'Turno não encontrado'),
            CASE 
                WHEN NEW.status = 'aceita' THEN 
                    CONCAT('Troca aprovada pelo administrador em ', NOW()::date, 
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
                WHEN NEW.status = 'cancelada' THEN
                    CONCAT('Solicitação cancelada pelo membro em ', COALESCE(NEW.data_cancelamento::date, NOW()::date))
                ELSE 
                    CONCAT('Troca rejeitada pelo administrador em ', NOW()::date,
                           CASE WHEN NEW.responsavel_aprovacao IS NOT NULL 
                                THEN CONCAT(' por ', NEW.responsavel_aprovacao) 
                                ELSE '' END)
            END
        FROM public."escalasPassadas" ep
        WHERE ep."ID" = NEW.id_escala_origem;
        
        RAISE NOTICE 'Histórico registrado para solicitação % com status %', NEW.id, NEW.status;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;