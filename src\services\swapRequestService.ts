
import { supabase } from '@/integrations/supabase/client';
import { SwapRequest } from '@/types/swapTypes';

export async function fetchMemberSwapRequests(memberId: string): Promise<SwapRequest[]> {
  try {
    const { data, error } = await supabase
      .from('solicitacoes_troca')
      .select('*')
      .or(`id_solicitante.eq.${memberId},membro_destino.eq.${memberId}`)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching member swap requests:', error);
      throw error;
    }

    if (!data) return [];
    
    return data.map(item => ({
      id: item.id,
      id_solicitante: item.id_solicitante,
      data_solicitacao: item.data_solicitacao,
      mensagem: item.mensagem || '',
      status: item.status as 'pendente' | 'aguardando_candidatos' | 'aguardando_aprovacao' | 'aceita' | 'rejeitada',
      created_at: item.created_at,
      updated_at: item.updated_at,
      data_resposta: item.data_resposta || undefined,
      id_escala_origem: item.id_escala_origem,
      id_escala_destino: item.id_escala_destino || undefined,
      membro_destino: item.membro_destino || undefined,
      tipo_solicitacao: item.tipo_solicitacao || 'troca',
      candidato_selecionado: item.candidato_selecionado || undefined,
      data_selecao_candidato: item.data_selecao_candidato || undefined,
      observacoes_admin: item.observacoes_admin || undefined,
      responsavel_aprovacao: item.responsavel_aprovacao || undefined
    }));
  } catch (error) {
    console.error('Failed to fetch member swap requests:', error);
    return [];
  }
}
