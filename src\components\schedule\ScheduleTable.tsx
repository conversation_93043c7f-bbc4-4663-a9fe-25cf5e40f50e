
import React, { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { getDayOfWeek, getShiftBackgroundColor } from '@/utils/dateUtils';
import { Person } from '@/types';
import PersonCell from './PersonCell';
import { supabase } from '@/integrations/supabase/client';

interface ScheduleTableProps {
  calendarRef: React.RefObject<HTMLDivElement>;
  sortedDays: Date[];
  schedule: Record<string, any>;
  persons: Person[];
  selectedPersons: any;
  maxPeopleCount: number;
  onPersonClick: (personId: string, personName: string, date: Date, shiftName: string) => void;
  showHighlightedEntries?: boolean;
}

// Helper function to get color based on service frequency status
const getServiceCountColor = (status: 'above' | 'average' | 'below'): string => {
  switch (status) {
    case 'above':
      return 'bg-red-500 text-white'; // Red for above average
    case 'average':
      return 'bg-amber-400 text-black'; // Yellow/amber for average
    case 'below':
      return 'bg-green-500 text-white'; // Green for below average
    default:
      return 'bg-gray-300 text-black'; // Default color
  }
};

const ScheduleTable: React.FC<ScheduleTableProps> = ({
  calendarRef,
  sortedDays,
  schedule,
  persons,
  selectedPersons,
  maxPeopleCount,
  onPersonClick,
  showHighlightedEntries = true,
}) => {
  // Track person occurrences
  const personOccurrences: Record<string, number> = {};
  
  const [memberServiceStats, setMemberServiceStats] = useState<Record<string, { count: number, status: 'above' | 'average' | 'below' }>>({});
  const [isLoading, setIsLoading] = useState(false);
  
  // Fetch member service statistics on component mount
  useEffect(() => {
    const fetchMemberServiceStats = async () => {
      try {
        setIsLoading(true);
        // Get current date
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        
        // Determine current semester (1 = Jan-Jun, 2 = Jul-Dec)
        const currentSemester = currentDate.getMonth() < 6 ? 1 : 2;
        
        // Set date range for current semester
        const startDate = currentSemester === 1 
          ? `01/01/${currentYear}` 
          : `01/07/${currentYear}`;
        
        const endDate = currentSemester === 1 
          ? `30/06/${currentYear}` 
          : `31/12/${currentYear}`;
        
        // Fetch all escalas records for the current semester
        const { data: escalasData, error: escalasError } = await supabase
          .from('escalasPassadas')
          .select('*')
          .gte('Data', startDate)
          .lte('Data', endDate);

        if (escalasError) throw escalasError;

        // Fetch all active members
        const { data: membrosData, error: membrosError } = await supabase
          .from('membros')
          .select('*')
          .eq('Status', 'ATIVO');

        if (membrosError) throw membrosError;

        const activeMembers = membrosData as any[];
        const totalActiveMembers = activeMembers.length;
        
        // Count services per member
        const serviceCount: Record<string, number> = {};
        
        if (escalasData) {
          escalasData.forEach((escala: any) => {
            // Count each position (Lider, Membro1, Membro2, etc.)
            ['Lider', 'Membro1', 'Membro2', 'Membro3', 'Membro4'].forEach(position => {
              const memberName = escala[position];
              if (memberName && typeof memberName === 'string') {
                serviceCount[memberName] = (serviceCount[memberName] || 0) + 1;
              }
            });
          });
        }
        
        // Calculate average services per member
        const totalServices = Object.values(serviceCount).reduce((sum, count) => sum + count, 0);
        const averageServices = totalActiveMembers > 0 ? totalServices / totalActiveMembers : 0;
        
        // Round the average to nearest whole number for comparison
        const roundedAverage = Math.round(averageServices);
        
        // Assign status to each member based on their service count
        const stats: Record<string, { count: number, status: 'above' | 'average' | 'below' }> = {};
        
        Object.entries(serviceCount).forEach(([member, count]) => {
          let status: 'above' | 'average' | 'below';
          
          if (count > roundedAverage) {
            status = 'above';
          } else if (count < roundedAverage) {
            status = 'below';
          } else {
            status = 'average';
          }
          
          stats[member] = { count, status };
        });
        
        setMemberServiceStats(stats);
      } catch (err) {
        console.error('Error fetching member service statistics:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMemberServiceStats();
  }, []);
  
  // Count occurrences of each person across all days and shifts
  sortedDays.forEach((day) => {
    const dateKey = format(day, "yyyy-MM-dd");
    const daySchedule = schedule[dateKey];
    if (daySchedule && daySchedule.shifts) {
      Object.values(daySchedule.shifts).forEach((shiftPersons: any) => {
        if (Array.isArray(shiftPersons)) {
          shiftPersons.forEach(personId => {
            personOccurrences[personId] = (personOccurrences[personId] || 0) + 1;
          });
        }
      });
    }
  });

  // Create a service frequency indicator component
  const ServiceFrequencyIndicator = ({ personName }: { personName: string }) => {
    const stats = memberServiceStats[personName];
    if (!stats) return null;
    
    return (
      <span 
        className={`inline-flex items-center justify-center w-5 h-5 text-xs font-semibold rounded-full ml-1 ${getServiceCountColor(stats.status)}`}
        title={`${stats.count} serviços este semestre`}
      >
        {stats.count}
      </span>
    );
  };
  
  return (
    <div
      ref={calendarRef}
      className="overflow-visible mt-4 p-4 bg-white rounded-lg print:p-2 print:mt-0 print:shadow-none print-container"
    >
      <div className="space-y-4 print:space-y-2">
        <div className="inline-block min-w-full print:w-full">
          <div className="border rounded-lg overflow-hidden print:border-0 print:rounded-none">
            <Table className="w-full table-fixed border-collapse print:w-[1000px] schedule-table">
              <TableHeader>
                <TableRow className="bg-muted">
                  {sortedDays.map((day) => {
                    const dayOfWeek = getDayOfWeek(day);
                    return (
                      <TableHead
                        key={day.toString()}
                        className="text-center font-medium whitespace-nowrap px-2 py-3 text-xs lg:text-sm border"
                        colSpan={1}
                      >
                        {dayOfWeek} - {format(day, "dd/MM")}
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  {sortedDays.map((day) => {
                    const dateKey = format(day, "yyyy-MM-dd");
                    const daySchedule = schedule[dateKey];
                    const dayOfWeek = getDayOfWeek(day);
                    
                    // Determine shift labels based on day of week
                    const shiftLabels = dayOfWeek === "Domingo"
                      ? [
                          { key: "morning", label: "EBD" },
                          { key: "afternoon", label: "1º Culto" },
                          { key: "evening", label: "2º Culto" }
                        ]
                      : [
                          { key: "morning", label: "Manhã" },
                          { key: "afternoon", label: "Tarde" },
                          { key: "evening", label: "Noite" }
                        ];
                    
                    return (
                      <TableCell key={`day-${day.toString()}`} className="p-0 align-top border">
                        <div className="flex flex-col h-full border rounded-md overflow-hidden">
                          <div className="bg-muted/50 p-1 text-center text-xs font-medium">
                            {dayOfWeek} - {format(day, "dd/MM")}
                          </div>
                          <div className="divide-y">
                            {shiftLabels.map(({key, label}) => {
                              const personIds = daySchedule?.shifts[key] || [];
                              
                              return (
                                <div
                                  key={`${day.toString()}-${key}`}
                                  className={`p-2 ${getShiftBackgroundColor(label)} flex flex-col items-center min-h-[60px]`}
                                >
                                  <h4 className="font-bold text-center mb-2 text-xs lg:text-sm">{label}</h4>
                                  <div className="w-full space-y-1">
                                    {personIds.length > 0 ? (
                                      personIds.map((id: string, idx: number) => {
                                        const person = persons.find(p => p.ID === id);
                                        if (!person) return null;
                                        
                                        const isRepeated = personOccurrences[id] > 1;
                                        const shouldHighlight = showHighlightedEntries && isRepeated;
                                        
                                        return (
                                          <div key={`${day.toString()}-${key}-${id}`} className="flex items-center">
                                            <PersonCell
                                              personId={id}
                                              personName={person.Nome}
                                              isFirstPerson={idx === 0}
                                              isSelected={selectedPersons?.id1 === id}
                                              isRecentlyAssigned={shouldHighlight}
                                              onClick={() => onPersonClick(id, person.Nome, day, label)}
                                            />
                                            {memberServiceStats && <ServiceFrequencyIndicator personName={person.Nome} />}
                                          </div>
                                        );
                                      })
                                    ) : (
                                      <div className="text-center text-xs text-muted-foreground py-2">
                                        Nenhum membro
                                      </div>
                                    )}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </TableCell>
                    );
                  })}
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleTable;
