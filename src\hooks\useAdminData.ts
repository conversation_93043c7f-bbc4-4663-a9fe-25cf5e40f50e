
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

export const useAdminData = () => {
  const { auth } = useAuth();
  const userEmail = auth.username;

  const { data: adminScaleName, isLoading: isAdminDataLoading } = useQuery({
    queryKey: ['adminData', userEmail],
    queryFn: async () => {
      if (!userEmail) {
        return null;
      }

      const { data, error } = await supabase
        .from('membros')
        .select('Nome Escala')
        .eq('email', userEmail)
        .maybeSingle();

      if (error) {
        console.error("Error fetching admin's scale name:", error.message);
        return null;
      }

      if (!data) {
        return null;
      }
      
      return data['Nome Escala'] || null;
    },
    enabled: !!userEmail,
    staleTime: 5 * 60 * 1000, // 5 minutes cache
  });

  return { adminScaleName, isAdminDataLoading };
};
