
import { startOfWeek, endOfWeek, addWeeks, format, isWithinInterval, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Report } from '@/services/api';

// Interface for chart data
export interface ChartData {
  week: string;
  Carros: number;
  Motos: number;
  Bicicletas: number;
  dateRange: string;
}

// Comparação de datas para ordenação (mais recente primeiro)
export const compareDates = (a: Report, b: Report) => {
  // Converte as datas para o formato Date (de DD/MM/YYYY para Date)
  const partsA = a.data.split('/');
  const partsB = b.data.split('/');
  
  const dateA = new Date(Number(partsA[2]), Number(partsA[1]) - 1, Number(partsA[0]));
  const dateB = new Date(Number(partsB[2]), Number(partsB[1]) - 1, Number(partsB[0]));
  
  // Ordem decrescente (mais recente primeiro)
  return dateB.getTime() - dateA.getTime();
};

// Helper function to convert DD/MM/YYYY to Date
export const parseBrazilianDate = (dateString: string): Date => {
  const [day, month, year] = dateString.split('/').map(num => parseInt(num, 10));
  return new Date(year, month - 1, day);
};

// Calculate vehicle chart data by week
export const getVehicleChartDataByWeek = (filteredData: Report[]): ChartData[] => {
  if (!filteredData.length) return [];
  
  // Find the earliest and latest dates in the filtered data
  let earliestDate = new Date();
  let latestDate = new Date(2000, 0, 1);
  
  filteredData.forEach(item => {
    if (!item.data) return;
    const date = parseBrazilianDate(item.data);
    if (date < earliestDate) earliestDate = date;
    if (date > latestDate) latestDate = date;
  });
  
  // Generate weeks from the earliest date
  // Updated to use Sunday (0) as start of week instead of Monday (1)
  const weeks: { start: Date; end: Date }[] = [];
  let currentWeekStart = startOfWeek(earliestDate, { weekStartsOn: 0 }); // 0 = Sunday (changed from 1 = Monday)
  
  while (currentWeekStart <= latestDate) {
    const currentWeekEnd = endOfWeek(currentWeekStart, { weekStartsOn: 0 }); // 0 = Sunday (changed from 1 = Monday)
    weeks.push({ 
      start: currentWeekStart, 
      end: currentWeekEnd 
    });
    currentWeekStart = addWeeks(currentWeekStart, 1);
  }
  
  // Group data by week
  const chartData: ChartData[] = weeks.map(week => {
    // Filter reports that fall within this week
    const weekReports = filteredData.filter(item => {
      if (!item.data) return false;
      const date = parseBrazilianDate(item.data);
      return isWithinInterval(date, { start: week.start, end: week.end });
    });
    
    // Sum up vehicle counts for this week
    const carros = weekReports.reduce((sum, item) => sum + (item.carros || 0), 0);
    const motos = weekReports.reduce((sum, item) => sum + (item.motos || 0), 0);
    const bikes = weekReports.reduce((sum, item) => sum + (item.bikes || 0), 0);
    
    // Format the week range (e.g., "26/01 - 01/02")
    const formattedStartDay = format(week.start, 'dd/MM', { locale: ptBR });
    const formattedEndDay = format(week.end, 'dd/MM', { locale: ptBR });
    const weekLabel = `${formattedStartDay} - ${formattedEndDay}`;
    
    return {
      week: weekLabel,
      Carros: carros,
      Motos: motos,
      Bicicletas: bikes,
      dateRange: weekLabel
    };
  });
  
  return chartData;
};

// Get total absences in the month
export const getTotalAbsences = (filteredData: Report[]): number => {
  return filteredData.reduce((total, item) => {
    const faltas = item.Faltas ? parseInt(item.Faltas) : 0;
    return total + (isNaN(faltas) ? 0 : faltas);
  }, 0);
};

// Get occurrences (filter out "NÃO. TUDO EM PAZ")
export const getOccurrences = (filteredData: Report[]): Report[] => {
  return filteredData.filter(item => {
    const alteracao = item["Alteração ?"];
    return alteracao && alteracao.toUpperCase() !== "NÃO. TUDO EM PAZ";
  });
};
