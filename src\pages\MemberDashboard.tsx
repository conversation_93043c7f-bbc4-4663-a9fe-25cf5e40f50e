
import React from 'react';
import { MemberLayout } from '@/components/MemberLayout';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { DashboardStats } from '@/components/dashboard/DashboardStats';
import { UpcomingSchedules } from '@/components/dashboard/UpcomingSchedules';
import { SwapRequests } from '@/components/dashboard/SwapRequests';
import { BirthdayList } from '@/components/dashboard/BirthdayList';
import { MemberSwapRequestsSummary } from '@/components/dashboard/MemberSwapRequestsSummary';
import { AvailabilityCard } from '@/components/dashboard/AvailabilityCard';
import { AdminSwapAlert } from '@/components/dashboard/AdminSwapAlert';
import { useMemberDashboardData } from '@/hooks/useMemberDashboardData';
import { useAdminPermissions } from '@/hooks/useAdminPermissions';

const MemberDashboard = () => {
  const { auth } = useMemberAuth();
  const { hasAdminAccess, memberCargo } = useAdminPermissions();
  const memberName = auth.member?.['Nome Escala'];
  
  const [showAllBirthdays, setShowAllBirthdays] = React.useState(false);

  const {
    swapRequests,
    upcomingSchedules,
    birthdays,
    availability
  } = useMemberDashboardData(
    memberName,
    !!auth.member,
    showAllBirthdays
  );

  // Buscar dados administrativos apenas se tiver permissão
  const {
    totalSwapRequestsCount,
    swapsWithCandidatesCount
  } = useMemberDashboardData('admin', hasAdminAccess);

  console.log('MemberDashboard - Admin Access:', {
    hasAdminAccess,
    memberCargo,
    totalSwapRequestsCount,
    swapsWithCandidatesCount
  });

  return (
    <MemberLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Bem-vindo, {auth.member?.NomeCompleto}!
          </h1>
          <p className="text-gray-600 mt-2">
            Acompanhe suas informações e atividades no sistema
          </p>
        </div>

        {/* Alerta administrativo apenas para cargos de liderança */}
        {hasAdminAccess && (
          <AdminSwapAlert 
            pendingCount={totalSwapRequestsCount || 0}
            withCandidatesCount={swapsWithCandidatesCount || 0}
          />
        )}

        <DashboardStats
          upcomingSchedulesCount={upcomingSchedules?.length || 0}
          swapRequestsCount={swapRequests?.length || 0}
          birthdaysCount={birthdays?.length || 0}
          memberStatus={auth.member?.Status || 'ATIVO'}
        />

        <UpcomingSchedules 
          schedules={upcomingSchedules} 
          memberName={memberName || ''}
        />
<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
  <MemberSwapRequestsSummary requests={swapRequests} />
  <SwapRequests requests={swapRequests} />
  <BirthdayList birthdays={birthdays} />
  
          
        </div>

        <AvailabilityCard availability={availability} />
      </div>
    </MemberLayout>
  );
};

export default MemberDashboard;
