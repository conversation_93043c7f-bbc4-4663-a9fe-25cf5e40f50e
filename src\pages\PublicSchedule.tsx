
import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Loading } from '@/components/ui/loading';
import { EscalasPassadas, Member } from '@/types/supabaseTypes';

const PublicSchedule = () => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [memberStats, setMemberStats] = useState<Record<string, { count: number, status: 'above' | 'average' | 'below' }>>({});

  useEffect(() => {
    const fetchLatestScheduleImage = async () => {
      try {
        setIsLoading(true);
        
        // Fetch the latest schedule with an image
        const { data, error } = await supabase
          .from('escalasPassadas')
          .select('*')
          .order('Data', { ascending: false })
          .limit(1);

        if (error) throw error;

        if (data && data.length > 0) {
          const latestSchedule = data[0] as EscalasPassadas;
          // Check if calendarImage exists in the database
          if (latestSchedule.calendarImage) {
            setImageUrl(latestSchedule.calendarImage);
          } else {
            // Try to get from schedule storage if available
            // This is just a placeholder, adjust according to where the images are actually stored
            const { data: storageData, error: storageError } = await supabase
              .storage
              .from('schedules')
              .list();

            if (storageError) throw storageError;

            if (storageData && storageData.length > 0) {
              // Get the latest image from storage
              const latestImage = storageData
                .filter(file => file.name.endsWith('.png') || file.name.endsWith('.jpg') || file.name.endsWith('.jpeg'))
                .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
              
              if (latestImage) {
                const { data: publicUrl } = supabase
                  .storage
                  .from('schedules')
                  .getPublicUrl(latestImage.name);
                
                setImageUrl(publicUrl.publicUrl);
              }
            }
          }
        }

        // Fetch member service statistics for the current semester
        await fetchMemberServiceStats();
      } catch (err) {
        console.error('Error fetching schedule image:', err);
        setError('Não foi possível carregar a escala. Por favor, tente novamente mais tarde.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchLatestScheduleImage();
  }, []);

  // Function to fetch member service statistics
  const fetchMemberServiceStats = async () => {
    try {
      // Get current date
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      
      // Determine current semester (1 = Jan-Jun, 2 = Jul-Dec)
      const currentSemester = currentDate.getMonth() < 6 ? 1 : 2;
      
      // Set date range for current semester
      const startDate = currentSemester === 1 
        ? `01/01/${currentYear}` 
        : `01/07/${currentYear}`;
      
      const endDate = currentSemester === 1 
        ? `30/06/${currentYear}` 
        : `31/12/${currentYear}`;
      
      // Fetch all escalas records for the current semester
      const { data: escalasData, error: escalasError } = await supabase
        .from('escalasPassadas')
        .select('*')
        .gte('Data', startDate)
        .lte('Data', endDate);

      if (escalasError) throw escalasError;

      // Fetch all active members
      const { data: membrosData, error: membrosError } = await supabase
        .from('membros')
        .select('*')
        .eq('Status', 'ATIVO');

      if (membrosError) throw membrosError;

      const activeMembers = membrosData as Member[];
      const totalActiveMembers = activeMembers.length;
      
      // Count services per member
      const serviceCount: Record<string, number> = {};
      
      if (escalasData) {
        escalasData.forEach(escala => {
          // Count each position (Lider, Membro1, Membro2, etc.)
          ['Lider', 'Membro1', 'Membro2', 'Membro3', 'Membro4'].forEach(position => {
            const memberName = escala[position as keyof typeof escala];
            if (memberName && typeof memberName === 'string') {
              serviceCount[memberName] = (serviceCount[memberName] || 0) + 1;
            }
          });
        });
      }
      
      // Calculate average services per member
      const totalServices = Object.values(serviceCount).reduce((sum, count) => sum + count, 0);
      const averageServices = totalActiveMembers > 0 ? totalServices / totalActiveMembers : 0;
      
      // Assign status to each member based on their service count
      const stats: Record<string, { count: number, status: 'above' | 'average' | 'below' }> = {};
      
      Object.entries(serviceCount).forEach(([member, count]) => {
        let status: 'above' | 'average' | 'below';
        
        if (count > averageServices) {
          status = 'above';
        } else if (count < averageServices) {
          status = 'below';
        } else {
          status = 'average';
        }
        
        stats[member] = { count, status };
      });
      
      setMemberStats(stats);
    } catch (err) {
      console.error('Error fetching member service statistics:', err);
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-xl font-semibold text-destructive mb-2">Erro</h1>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (!imageUrl) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <h1 className="text-xl font-semibold mb-2">Nenhuma escala disponível</h1>
          <p>Não há escalas publicadas no momento.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-background p-4">
      <div className="max-w-full overflow-auto">
        <img 
          src={imageUrl} 
          alt="Escala de trabalho" 
          className="w-full h-auto object-contain max-h-screen"
        />
      </div>
    </div>
  );
};

export default PublicSchedule;
