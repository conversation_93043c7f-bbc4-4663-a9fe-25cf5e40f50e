
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { Calendar as CalendarIcon, Save } from 'lucide-react';

interface UnavailabilityFormProps {
  selectedDates: Date[];
  onDatesChange: (dates: Date[]) => void;
  motivo: string;
  onMotivoChange: (motivo: string) => void;
  onSave: () => void;
  isSaving: boolean;
  unavailableDates: Date[];
}

export const UnavailabilityForm = ({
  selectedDates,
  onDatesChange,
  motivo,
  onMotivoChange,
  onSave,
  isSaving,
  unavailableDates
}: UnavailabilityFormProps) => {
  const isDateUnavailable = (date: Date) => {
    return unavailableDates.some(unavailableDate => 
      unavailableDate.getFullYear() === date.getFullYear() &&
      unavailableDate.getMonth() === date.getMonth() &&
      unavailableDate.getDate() === date.getDate()
    );
  };

  const formatSelectedDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarIcon className="w-5 h-5" />
          Adicionar Datas de Indisponibilidade
        </CardTitle>
        <CardDescription>
          Selecione as datas em que você não estará disponível para servir
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Calendar
            mode="multiple"
            selected={selectedDates}
            onSelect={(dates) => onDatesChange(dates || [])}
            disabled={(date) => date < new Date() || isDateUnavailable(date)}
            className="rounded-md border"
          />
        </div>
        
        {selectedDates.length > 0 && (
          <div className="space-y-4">
            <div>
              <Textarea
                placeholder="Informe o motivo da indisponibilidade..."
                value={motivo}
                onChange={(e) => onMotivoChange(e.target.value)}
              />
            </div>
            
            <div className="flex flex-wrap gap-2">
              <p className="text-sm text-gray-600 w-full">Datas selecionadas:</p>
              {selectedDates.map((date, index) => (
                <Badge key={index} variant="secondary">
                  {formatSelectedDate(date)}
                </Badge>
              ))}
            </div>
            
            <Button 
              onClick={onSave}
              disabled={isSaving}
              className="w-full"
            >
              <Save className="w-4 h-4 mr-2" />
              {isSaving ? 'Salvando...' : 'Salvar Indisponibilidades'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
