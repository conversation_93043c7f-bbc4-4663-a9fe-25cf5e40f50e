
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ScheduleFooterProps {
  handleBack: () => void;
  handleSave: () => void;
  sending: boolean;
  disabled: boolean;
}

const ScheduleFooter: React.FC<ScheduleFooterProps> = ({
  handleBack,
  handleSave,
  sending,
  disabled,
}) => {
  return (
    <div className="flex justify-between mt-8">
      <Button 
        onClick={handleBack} 
        variant="outline"
      >
        Voltar
      </Button>
      
      <Button 
        onClick={handleSave}
        disabled={sending || disabled}
        className="bg-primary hover:bg-primary/90 text-white"
      >
        {sending ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Salvando...
          </>
        ) : "Salvar Escala"}
      </Button>
    </div>
  );
};

export default ScheduleFooter;
