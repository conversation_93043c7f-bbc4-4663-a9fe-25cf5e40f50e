export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      api_disponibilidade: {
        Row: {
          Domingo: string | null
          ID: number
          Nome: string
          "Quarta-Feira": string | null
          "Quinta-Feira": string | null
          Sabado: string | null
          "Segunda-Feira": string | null
          "Sexta-Feira": string | null
          "Terça-Feira": string | null
        }
        Insert: {
          Domingo?: string | null
          ID?: number
          Nome?: string
          "Quarta-Feira"?: string | null
          "Quinta-Feira"?: string | null
          Sabado?: string | null
          "Segunda-Feira"?: string | null
          "Sexta-Feira"?: string | null
          "Terça-Feira"?: string | null
        }
        Update: {
          Domingo?: string | null
          ID?: number
          Nome?: string
          "Quarta-Feira"?: string | null
          "Quinta-Feira"?: string | null
          Sabado?: string | null
          "Segunda-Feira"?: string | null
          "Sexta-Feira"?: string | null
          "Terça-Feira"?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "api_disponibilidade_Nome_fkey"
            columns: ["Nome"]
            isOneToOne: true
            referencedRelation: "membros"
            referencedColumns: ["Nome Escala"]
          },
        ]
      }
      candidaturas_troca: {
        Row: {
          created_at: string
          data_candidatura: string
          id: string
          id_solicitacao: string
          membro_candidato: string
          observacoes: string | null
          status: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          data_candidatura?: string
          id?: string
          id_solicitacao: string
          membro_candidato: string
          observacoes?: string | null
          status?: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          data_candidatura?: string
          id?: string
          id_solicitacao?: string
          membro_candidato?: string
          observacoes?: string | null
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "candidaturas_troca_id_solicitacao_fkey"
            columns: ["id_solicitacao"]
            isOneToOne: false
            referencedRelation: "solicitacoes_troca"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "candidaturas_troca_membro_candidato_fkey"
            columns: ["membro_candidato"]
            isOneToOne: false
            referencedRelation: "membros"
            referencedColumns: ["Nome Escala"]
          },
        ]
      }
      contatoswhats: {
        Row: {
          descrição: string | null
          id_grupo: string
          "nome grupo": string | null
          proprietário: string | null
          Quantidade: string | null
        }
        Insert: {
          descrição?: string | null
          id_grupo?: string
          "nome grupo"?: string | null
          proprietário?: string | null
          Quantidade?: string | null
        }
        Update: {
          descrição?: string | null
          id_grupo?: string
          "nome grupo"?: string | null
          proprietário?: string | null
          Quantidade?: string | null
        }
        Relationships: []
      }
      disponibilidade: {
        Row: {
          Domingo: string | null
          ID: string | null
          "ID DiasAservir": string
          "ID NUMBER": string | null
          OBSERVAÇÕES: string | null
          "Quarta-Feira": string | null
          "Quem atualizou a Escala": string | null
          "Quinta-Feira": string | null
          Sabado: string | null
          "Segunda-Feira": string | null
          "Sexta-Feira": string | null
          "Status Escala": string | null
          STATUS2: string | null
          "Terça-Feira": string | null
        }
        Insert: {
          Domingo?: string | null
          ID?: string | null
          "ID DiasAservir": string
          "ID NUMBER"?: string | null
          OBSERVAÇÕES?: string | null
          "Quarta-Feira"?: string | null
          "Quem atualizou a Escala"?: string | null
          "Quinta-Feira"?: string | null
          Sabado?: string | null
          "Segunda-Feira"?: string | null
          "Sexta-Feira"?: string | null
          "Status Escala"?: string | null
          STATUS2?: string | null
          "Terça-Feira"?: string | null
        }
        Update: {
          Domingo?: string | null
          ID?: string | null
          "ID DiasAservir"?: string
          "ID NUMBER"?: string | null
          OBSERVAÇÕES?: string | null
          "Quarta-Feira"?: string | null
          "Quem atualizou a Escala"?: string | null
          "Quinta-Feira"?: string | null
          Sabado?: string | null
          "Segunda-Feira"?: string | null
          "Sexta-Feira"?: string | null
          "Status Escala"?: string | null
          STATUS2?: string | null
          "Terça-Feira"?: string | null
        }
        Relationships: []
      }
      disponibilidade_membros: {
        Row: {
          created_at: string
          data_indisponivel: string
          id: string
          membro_nome: string
          motivo: string | null
        }
        Insert: {
          created_at?: string
          data_indisponivel: string
          id?: string
          membro_nome: string
          motivo?: string | null
        }
        Update: {
          created_at?: string
          data_indisponivel?: string
          id?: string
          membro_nome?: string
          motivo?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "disponibilidade_membros_membro_nome_fkey"
            columns: ["membro_nome"]
            isOneToOne: false
            referencedRelation: "membros"
            referencedColumns: ["Nome Escala"]
          },
        ]
      }
      endpointEvolution: {
        Row: {
          Autenticacao: string
          BaseUrl: string
          Desconexao: string
          Instancia: string
          QrCode: string
          Reinicializacao: string
          Status: string
        }
        Insert: {
          Autenticacao: string
          BaseUrl: string
          Desconexao: string
          Instancia: string
          QrCode: string
          Reinicializacao: string
          Status: string
        }
        Update: {
          Autenticacao?: string
          BaseUrl?: string
          Desconexao?: string
          Instancia?: string
          QrCode?: string
          Reinicializacao?: string
          Status?: string
        }
        Relationships: []
      }
      escalasPassadas: {
        Row: {
          Data: string | null
          Dia: string | null
          "ENVIO PESSOAL": string | null
          ID: string
          Lider: string | null
          Membro1: string | null
          Membro2: string | null
          Membro3: string | null
          Membro4: string | null
          "STATUS ALERTA": string | null
          Turno: string | null
        }
        Insert: {
          Data?: string | null
          Dia?: string | null
          "ENVIO PESSOAL"?: string | null
          ID: string
          Lider?: string | null
          Membro1?: string | null
          Membro2?: string | null
          Membro3?: string | null
          Membro4?: string | null
          "STATUS ALERTA"?: string | null
          Turno?: string | null
        }
        Update: {
          Data?: string | null
          Dia?: string | null
          "ENVIO PESSOAL"?: string | null
          ID?: string
          Lider?: string | null
          Membro1?: string | null
          Membro2?: string | null
          Membro3?: string | null
          Membro4?: string | null
          "STATUS ALERTA"?: string | null
          Turno?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "escalasPassadas_Lider_fkey"
            columns: ["Lider"]
            isOneToOne: false
            referencedRelation: "membros"
            referencedColumns: ["Nome Escala"]
          },
        ]
      }
      evolutionAPI: {
        Row: {
          created_at: string
          Email: string
          id: number
          Instancia: string | null
          Senha: string
          Telefone: string
        }
        Insert: {
          created_at?: string
          Email: string
          id?: number
          Instancia?: string | null
          Senha: string
          Telefone?: string
        }
        Update: {
          created_at?: string
          Email?: string
          id?: number
          Instancia?: string | null
          Senha?: string
          Telefone?: string
        }
        Relationships: []
      }
      historico_trocas: {
        Row: {
          escala_destino_data: string | null
          escala_destino_turno: string | null
          escala_origem_data: string
          escala_origem_turno: string
          id: string
          id_solicitacao: string
          membro_destino: string
          membro_origem: string
          observacoes: string | null
          realizada_em: string
          tipo_troca: string
        }
        Insert: {
          escala_destino_data?: string | null
          escala_destino_turno?: string | null
          escala_origem_data: string
          escala_origem_turno: string
          id?: string
          id_solicitacao: string
          membro_destino: string
          membro_origem: string
          observacoes?: string | null
          realizada_em?: string
          tipo_troca: string
        }
        Update: {
          escala_destino_data?: string | null
          escala_destino_turno?: string | null
          escala_origem_data?: string
          escala_origem_turno?: string
          id?: string
          id_solicitacao?: string
          membro_destino?: string
          membro_origem?: string
          observacoes?: string | null
          realizada_em?: string
          tipo_troca?: string
        }
        Relationships: [
          {
            foreignKeyName: "historico_trocas_id_solicitacao_fkey"
            columns: ["id_solicitacao"]
            isOneToOne: false
            referencedRelation: "solicitacoes_troca"
            referencedColumns: ["id"]
          },
        ]
      }
      membros: {
        Row: {
          Atualizacao_dataHora: string | null
          CargoMinisterio: string | null
          CPF: string | null
          DataNascimento: string | null
          email: string | null
          Endereço: string | null
          Equipe: string | null
          EstadoCivil: string | null
          Foto: string | null
          foto_url: string | null
          "Link Foto": string | null
          "Nome Escala": string
          NomeCompleto: string | null
          Profissão: string | null
          Status: string | null
          Telefone: string | null
        }
        Insert: {
          Atualizacao_dataHora?: string | null
          CargoMinisterio?: string | null
          CPF?: string | null
          DataNascimento?: string | null
          email?: string | null
          Endereço?: string | null
          Equipe?: string | null
          EstadoCivil?: string | null
          Foto?: string | null
          foto_url?: string | null
          "Link Foto"?: string | null
          "Nome Escala": string
          NomeCompleto?: string | null
          Profissão?: string | null
          Status?: string | null
          Telefone?: string | null
        }
        Update: {
          Atualizacao_dataHora?: string | null
          CargoMinisterio?: string | null
          CPF?: string | null
          DataNascimento?: string | null
          email?: string | null
          Endereço?: string | null
          Equipe?: string | null
          EstadoCivil?: string | null
          Foto?: string | null
          foto_url?: string | null
          "Link Foto"?: string | null
          "Nome Escala"?: string
          NomeCompleto?: string | null
          Profissão?: string | null
          Status?: string | null
          Telefone?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          created_at: string
          id: string
          username: string
        }
        Insert: {
          created_at?: string
          id: string
          username: string
        }
        Update: {
          created_at?: string
          id?: string
          username?: string
        }
        Relationships: []
      }
      relatorio_sentinela: {
        Row: {
          "Alteração ?": string | null
          bikes: number | null
          carros: number | null
          data: string | null
          Desatentos: string | null
          "enviado lider ?": string | null
          Faltas: string | null
          id: string
          Motorista1: string | null
          Motorista2: string | null
          motos: number | null
          "Nome Lider": string
          Relatado: string | null
          Relator: string | null
          "Tel relator": string | null
          "Tel Testemunha": string | null
          "Tel Testemunha2": string | null
          "Tel vit 1": string | null
          "Tel vit 2": string | null
          "Tel vit 3": string | null
          "Tel vit 4": string | null
          "Tel vit 5": string | null
          tel_moto: string | null
          tel_moto2: string | null
          Testemunha1: string | null
          Testemunha2: string | null
          "Tipo Culto": string | null
          "Versão sentinela": string | null
          Vitima1: string | null
          Vitima2: string | null
          Vitima3: string | null
          Vitima4: string | null
          Vitima5: string | null
        }
        Insert: {
          "Alteração ?"?: string | null
          bikes?: number | null
          carros?: number | null
          data?: string | null
          Desatentos?: string | null
          "enviado lider ?"?: string | null
          Faltas?: string | null
          id: string
          Motorista1?: string | null
          Motorista2?: string | null
          motos?: number | null
          "Nome Lider": string
          Relatado?: string | null
          Relator?: string | null
          "Tel relator"?: string | null
          "Tel Testemunha"?: string | null
          "Tel Testemunha2"?: string | null
          "Tel vit 1"?: string | null
          "Tel vit 2"?: string | null
          "Tel vit 3"?: string | null
          "Tel vit 4"?: string | null
          "Tel vit 5"?: string | null
          tel_moto?: string | null
          tel_moto2?: string | null
          Testemunha1?: string | null
          Testemunha2?: string | null
          "Tipo Culto"?: string | null
          "Versão sentinela"?: string | null
          Vitima1?: string | null
          Vitima2?: string | null
          Vitima3?: string | null
          Vitima4?: string | null
          Vitima5?: string | null
        }
        Update: {
          "Alteração ?"?: string | null
          bikes?: number | null
          carros?: number | null
          data?: string | null
          Desatentos?: string | null
          "enviado lider ?"?: string | null
          Faltas?: string | null
          id?: string
          Motorista1?: string | null
          Motorista2?: string | null
          motos?: number | null
          "Nome Lider"?: string
          Relatado?: string | null
          Relator?: string | null
          "Tel relator"?: string | null
          "Tel Testemunha"?: string | null
          "Tel Testemunha2"?: string | null
          "Tel vit 1"?: string | null
          "Tel vit 2"?: string | null
          "Tel vit 3"?: string | null
          "Tel vit 4"?: string | null
          "Tel vit 5"?: string | null
          tel_moto?: string | null
          tel_moto2?: string | null
          Testemunha1?: string | null
          Testemunha2?: string | null
          "Tipo Culto"?: string | null
          "Versão sentinela"?: string | null
          Vitima1?: string | null
          Vitima2?: string | null
          Vitima3?: string | null
          Vitima4?: string | null
          Vitima5?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "relatorio_sentinela_Nome Lider_fkey2"
            columns: ["Nome Lider"]
            isOneToOne: false
            referencedRelation: "membros"
            referencedColumns: ["Nome Escala"]
          },
        ]
      }
      solicitacoes_troca: {
        Row: {
          candidato_selecionado: string | null
          created_at: string
          data_resposta: string | null
          data_selecao_candidato: string | null
          data_solicitacao: string
          id: string
          id_escala_destino: string | null
          id_escala_origem: string
          id_solicitante: string
          membro_destino: string | null
          mensagem: string | null
          observacoes_admin: string | null
          responsavel_aprovacao: string | null
          status: string
          tipo_solicitacao: string
          updated_at: string
        }
        Insert: {
          candidato_selecionado?: string | null
          created_at?: string
          data_resposta?: string | null
          data_selecao_candidato?: string | null
          data_solicitacao?: string
          id?: string
          id_escala_destino?: string | null
          id_escala_origem: string
          id_solicitante: string
          membro_destino?: string | null
          mensagem?: string | null
          observacoes_admin?: string | null
          responsavel_aprovacao?: string | null
          status?: string
          tipo_solicitacao: string
          updated_at?: string
        }
        Update: {
          candidato_selecionado?: string | null
          created_at?: string
          data_resposta?: string | null
          data_selecao_candidato?: string | null
          data_solicitacao?: string
          id?: string
          id_escala_destino?: string | null
          id_escala_origem?: string
          id_solicitante?: string
          membro_destino?: string | null
          mensagem?: string | null
          observacoes_admin?: string | null
          responsavel_aprovacao?: string | null
          status?: string
          tipo_solicitacao?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "solicitacoes_troca_candidato_selecionado_fkey"
            columns: ["candidato_selecionado"]
            isOneToOne: false
            referencedRelation: "membros"
            referencedColumns: ["Nome Escala"]
          },
          {
            foreignKeyName: "solicitacoes_troca_id_escala_origem_fkey"
            columns: ["id_escala_origem"]
            isOneToOne: false
            referencedRelation: "escalasPassadas"
            referencedColumns: ["ID"]
          },
          {
            foreignKeyName: "solicitacoes_troca_id_solicitante_fkey"
            columns: ["id_solicitante"]
            isOneToOne: false
            referencedRelation: "api_disponibilidade"
            referencedColumns: ["Nome"]
          },
          {
            foreignKeyName: "solicitacoes_troca_membro_destino_fkey"
            columns: ["membro_destino"]
            isOneToOne: false
            referencedRelation: "membros"
            referencedColumns: ["Nome Escala"]
          },
        ]
      }
      Vendedor_teste: {
        Row: {
          atendimento: Database["public"]["Enums"]["atendimento"] | null
          created_at: string
          nivel: Database["public"]["Enums"]["nivel"] | null
          nome: string | null
          status: Database["public"]["Enums"]["status"] | null
          telefone: string
          thread: string | null
          uuid: string
        }
        Insert: {
          atendimento?: Database["public"]["Enums"]["atendimento"] | null
          created_at?: string
          nivel?: Database["public"]["Enums"]["nivel"] | null
          nome?: string | null
          status?: Database["public"]["Enums"]["status"] | null
          telefone?: string
          thread?: string | null
          uuid?: string
        }
        Update: {
          atendimento?: Database["public"]["Enums"]["atendimento"] | null
          created_at?: string
          nivel?: Database["public"]["Enums"]["nivel"] | null
          nome?: string | null
          status?: Database["public"]["Enums"]["status"] | null
          telefone?: string
          thread?: string | null
          uuid?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      atualizar_solicitacao_e_substituir: {
        Args: { p_id_solicitacao: number; p_id_escala_origem: number }
        Returns: undefined
      }
      is_admin_user: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      set_current_member: {
        Args: Record<PropertyKey, never> | { member_name: string }
        Returns: undefined
      }
    }
    Enums: {
      atendimento: "consulta" | "venda"
      nivel: "lead" | "cliente" | "contato"
      status: "quente" | "morno" | "frio"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      atendimento: ["consulta", "venda"],
      nivel: ["lead", "cliente", "contato"],
      status: ["quente", "morno", "frio"],
    },
  },
} as const
