
import React from 'react';

const CalendarHeader = () => {
  const weekDays = ["Segunda", "Ter<PERSON>", "Quarta", "Q<PERSON><PERSON>", "<PERSON><PERSON>", "Sábado", "Domingo"];
  
  return (
    <div className="grid grid-cols-7 gap-1 mb-1">
      {weekDays.map((day, index) => (
        <div 
          key={index} 
          className="p-2 text-center font-medium bg-muted text-muted-foreground text-sm"
        >
          {day}
        </div>
      ))}
    </div>
  );
};

export default CalendarHeader;
