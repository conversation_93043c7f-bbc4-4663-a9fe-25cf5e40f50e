import React, { useState, useContext } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, X } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface MemberSwapCardContextType {
  expandedId: string | null;
  setExpandedId: (id: string | null) => void;
}

export const MemberSwapCardContext = React.createContext<MemberSwapCardContextType>({
  expandedId: null,
  setExpandedId: () => {},
});

interface MemberSwapCardProps {
  request: SwapRequest & { scheduleData?: any; id: string };
  candidatesCount?: number;
  onApply?: () => void;
  onCancel?: () => void;
}

const formatDateBR = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'dd/MM/yyyy', { locale: ptBR });
  } catch {
    return dateString;
  }
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'aguardando_candidatos':
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Aguardando Voluntários</Badge>;
    case 'aguardando_aprovacao':
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Em Análise</Badge>;
    case 'aceita':
      return <Badge variant="secondary" className="bg-green-100 text-green-800">Aprovada</Badge>;
    case 'rejeitada':
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejeitada</Badge>;
    case 'cancelada':
      return <Badge variant="secondary">Cancelada</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

export const MemberSwapCard = ({
  request,
  candidatesCount = 0,
  onCancel,
}: MemberSwapCardProps) => {
  const { expandedId, setExpandedId } = useContext(MemberSwapCardContext);
  const isOpen = expandedId === request.id;
  const showApplyButton = request.status === 'aguardando_candidatos';
  const [isCanceling, setIsCanceling] = useState(false);
  const [cancelError, setCancelError] = useState<string | null>(null);

  const handleToggle = () => {
    setExpandedId(isOpen ? null : request.id);
  };

  return (
    <Card className="border-border cursor-pointer" onClick={handleToggle}>
      <CardContent className="p-4">
        {/* Seção Pai - Informações Resumidas */}
        <div className="flex justify-between items-center gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 truncate">
              <Calendar className="w-4 h-4 text-primary flex-shrink-0" />
              <span className="truncate">
                {formatDateBR(request.scheduleData?.Data)} • {request.scheduleData?.Turno}
                <span className="text-muted-foreground ml-1">({request.scheduleData?.Dia})</span>
              </span>
            </div>
            
            {!isOpen && request.mensagem && (
              <div className="text-sm text-muted-foreground truncate mt-1">
                {request.mensagem}
              </div>
            )}
          </div>
          
                
          <div className="ml-2 flex-shrink-0">
            {getStatusBadge(request.status)}
          </div>
          <div className={`transition-transform ${isOpen ? 'rotate-180' : ''} flex-shrink-0`}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {/* Seção Detalhada - Apenas quando expandido */}
        {isOpen && (
        <Card className="border-border mt-4">
          <CardContent className="p-4 space-y-2">
          <div className="flex justify-between">
            <div className="flex items-center gap-2">
              <span className="font-medium">Data solicitação:</span>
              <span>{formatDateBR(request.data_solicitacao)}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Candidaturas:</span>
              <span>{candidatesCount || 0}</span>
            </div>
          </div>

          {request.mensagem && (
            <div>
              <div className="font-medium">Motivo:</div>
              <div className="text-sm text-muted-foreground">
                {request.mensagem}
              </div>
            </div>
          )}

          {showApplyButton && onCancel && (
            <div className="flex justify-end pt-2 space-y-2 flex-col">
              {cancelError && (
                <div className="text-red-500 text-sm">{cancelError}</div>
              )}
              <Button
                variant="destructive"
                size="sm"
                onClick={async () => {
                  try {
                    setIsCanceling(true);
                    setCancelError(null);
                    await onCancel();
                  } catch (error) {
                    setCancelError('Falha ao cancelar solicitação. Tente novamente.');
                    console.error('Cancel error:', error);
                  } finally {
                    setIsCanceling(false);
                  }
                }}
                className="flex items-center gap-2"
                disabled={isCanceling}
              >
                {isCanceling ? (
                  <>
                    <span className="animate-spin">↻</span>
                    Cancelando...
                  </>
                ) : (
                  <>
                    <X className="w-4 h-4" />
                    Cancelar Solicitação
                  </>
                )}
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
      )}
      </CardContent>
    </Card>
  );
};