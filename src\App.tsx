
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { MemberAuthProvider } from "@/contexts/MemberAuthContext";
import { ScheduleProvider } from "@/contexts/ScheduleContext";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Calendar from "./pages/Calendar";
import Members from "./pages/Members";
import Dashboard from "./pages/Dashboard";
import NotFound from "./pages/NotFound";
import Settings from "./pages/Settings";
import PastSchedules from "./pages/PastSchedules";
import ScheduleView from "./pages/ScheduleView";
import PublicSchedule from "./pages/PublicSchedule";
import AdminSwapRequests from "./pages/AdminSwapRequests";

// Member Portal Pages
import MemberLogin from "./pages/MemberLogin";
import MemberDashboard from "./pages/MemberDashboard";
import MemberSchedules from "./pages/MemberSchedules";
import MemberAvailability from "./pages/MemberAvailability";
import MemberSwaps from "./pages/MemberSwaps";
import MemberProfile from "./pages/MemberProfile";

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <MemberAuthProvider>
            <AuthProvider>
              <ScheduleProvider>
                <Routes>
                  {/* Public routes */}
                  <Route path="/" element={<Index />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/public/:scheduleId" element={<PublicSchedule />} />
                  
                  {/* Admin routes */}
                  <Route path="/calendar" element={<Calendar />} />
                  <Route path="/members" element={<Members />} />
                  <Route path="/membros" element={<Members />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/settings" element={<Settings />} />
                  <Route path="/past-schedules" element={<PastSchedules />} />
                  <Route path="/schedule/:id" element={<ScheduleView />} />
                  <Route path="/schedule-view" element={<ScheduleView />} />
                  <Route path="/admin-swap-requests" element={<AdminSwapRequests />} />
                  <Route path="/trocas" element={<AdminSwapRequests />} />
                  
                  {/* Member Portal routes */}
                  <Route path="/membro-login" element={<MemberLogin />} />
                  <Route path="/membro-dashboard" element={<MemberDashboard />} />
                  <Route path="/membro-escalas" element={<MemberSchedules />} />
                  <Route path="/membro-disponibilidade" element={<MemberAvailability />} />
                  <Route path="/membro-trocas" element={<MemberSwaps />} />
                  <Route path="/membro-troca" element={<MemberSwaps />} />
                  <Route path="/membro-perfil" element={<MemberProfile />} />
                  
                  {/* Catch all */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </ScheduleProvider>
            </AuthProvider>
          </MemberAuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
