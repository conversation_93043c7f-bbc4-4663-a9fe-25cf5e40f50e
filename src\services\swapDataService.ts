
import { supabase } from '@/integrations/supabase/client';
import { SwapRequest } from '@/types/swapTypes';

export async function fetchSwapRequestsWithScheduleData(currentYear?: number, currentMonth?: number): Promise<SwapRequest[]> {
  try {
    console.log('Fetching swap requests with schedule data for admin...');
    
    let query = supabase
      .from('solicitacoes_troca')
      .select(`
        *,
        escalasPassadas!inner(
          ID,
          Data,
          Turno,
          Dia,
          Lider,
          Membro1,
          Membro2,
          Membro3,
          Membro4
        )
      `)
      .order('created_at', { ascending: false });

    // Aplicar filtro temporal se fornecido
    if (currentYear && currentMonth) {
      const dateFilter = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-01`;
      query = query.gte('escalasPassadas.Data', dateFilter);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching swap requests with schedule data:', error);
      throw error;
    }

    console.log('Raw data from Supabase:', data);

    // Transformar os dados para incluir scheduleData
    const transformedData = (data || []).map(item => ({
      ...item,
      scheduleData: item.escalasPassadas || null
    }));

    console.log('Transformed swap requests with schedule data:', transformedData);
    return transformedData as SwapRequest[];
  } catch (error) {
    console.error('Failed to fetch swap requests with schedule data:', error);
    throw new Error('Falha ao carregar solicitações de troca com dados de escala');
  }
}
