-- Otimizações de performance para consultas de troca
-- Execute este script no Supabase SQL Editor para melhorar a performance

-- Índices para melhorar performance das consultas de solicitações de troca
CREATE INDEX IF NOT EXISTS idx_solicitacoes_troca_solicitante_status 
ON solicitacoes_troca (id_solicitante, status);

CREATE INDEX IF NOT EXISTS idx_solicitacoes_troca_status_created 
ON solicitacoes_troca (status, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_solicitacoes_troca_escala_origem 
ON solicitacoes_troca (id_escala_origem);

-- Índices para escalasPassadas para melhorar joins
CREATE INDEX IF NOT EXISTS idx_escalas_passadas_data 
ON "escalasPassadas" ("Data");

CREATE INDEX IF NOT EXISTS idx_escalas_passadas_membros 
ON "escalasPassadas" ("Lider", "Membro1", "Membro2", "Membro3", "Membro4");

-- Índices para candidaturas
CREATE INDEX IF NOT EXISTS idx_candidaturas_membro_status 
ON candidaturas_troca (membro_candidato, status);

CREATE INDEX IF NOT EXISTS idx_candidaturas_solicitacao 
ON candidaturas_troca (id_solicitacao);

-- Índice composto para disponibilidade
CREATE INDEX IF NOT EXISTS idx_disponibilidade_proprietario 
ON disponibilidade ("proprietário", "Quem atualizou a Escala");

-- Análise das tabelas para otimizar o planner
ANALYZE solicitacoes_troca;
ANALYZE "escalasPassadas";
ANALYZE candidaturas_troca;
ANALYZE disponibilidade;
