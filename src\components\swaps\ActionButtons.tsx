import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface ActionButtonsProps {
  currentStatus: string;
  onStatusChange: (newStatus: string) => void;
  isLoading?: boolean;
}

export default function ActionButtons({ 
  currentStatus,
  onStatusChange,
  isLoading = false
}: ActionButtonsProps) {
  const isPending = currentStatus === 'pending';

  return (
    <div className="flex gap-2 mt-4">
      {isPending && (
        <>
          <Button
            variant="default"
            className="bg-green-600 hover:bg-green-700 text-white"
            size="sm"
            onClick={() => onStatusChange('approved')}
            disabled={isLoading}
          >
            {isLoading ? <Loader2 className="animate-spin" /> : 'Aprovar'}
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onStatusChange('rejected')}
            disabled={isLoading}
          >
            {isLoading ? <Loader2 className="animate-spin" /> : 'Rejeitar'}
          </Button>
        </>
      )}
      
      {!isPending && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => onStatusChange('pending')}
          disabled={isLoading}
        >
          {isLoading ? <Loader2 className="animate-spin" /> : 'Reabrir'}
        </Button>
      )}
    </div>
  );
}