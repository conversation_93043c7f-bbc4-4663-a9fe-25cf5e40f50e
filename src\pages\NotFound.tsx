
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';

const NotFound = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-primary">404</h1>
        <p className="text-2xl font-medium mt-4">Página não encontrada</p>
        <p className="text-muted-foreground mt-2 mb-6">
          A página que você está procurando não existe.
        </p>
        <Button onClick={() => navigate('/')}>Voltar para o Início</Button>
      </div>
    </div>
  );
};

export default NotFound;
