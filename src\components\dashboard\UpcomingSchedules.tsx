
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar } from 'lucide-react';

interface Schedule {
  ID: string;
  Data: string;
  Turno: string;
  Dia: string;
  Lider: string;
  Membro1?: string;
  Membro2?: string;
  Membro3?: string;
  Membro4?: string;
}

interface UpcomingSchedulesProps {
  schedules: Schedule[] | undefined;
  memberName: string;
}

export const UpcomingSchedules = ({ schedules, memberName }: UpcomingSchedulesProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Próximas Escalas</CardTitle>
        <CardDescription>Suas próximas atividades programadas</CardDescription>
      </CardHeader>
      <CardContent>
        {schedules && schedules.length > 0 ? (
          <div className="space-y-3">
            {schedules.map((schedule, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">{schedule.Data} - {schedule.Turno}</p>
                  <p className="text-sm text-gray-600">{schedule.Dia}</p>
                </div>
                <Badge variant="outline">
                  {schedule.Lider === memberName ? 'Líder' : 'Membro'}
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Nenhuma escala programada</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
